import requests
import hashlib
import time
import json
import logging
import os
from datetime import datetime
import threading
import tkinter as tk
from tkinter import messagebox, ttk
import queue
import re
import configparser

# 条件导入 - 只在需要时导入
try:
    import pystray
    from PIL import Image, ImageDraw
    HAS_TRAY = True
except ImportError:
    HAS_TRAY = False

try:
    import pyperclip
    HAS_CLIPBOARD = True
except ImportError:
    HAS_CLIPBOARD = False

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    HAS_WATCHDOG = True
except ImportError:
    HAS_WATCHDOG = False

try:
    import pyautogui
    HAS_AUTOGUI = True
except ImportError:
    HAS_AUTOGUI = False

try:
    import keyboard
    HAS_KEYBOARD = True
except ImportError:
    HAS_KEYBOARD = False

# 配置信息（请根据实际环境修改）
BASE_URL = "ymt.cslg.edu.cn:8081/easytong_app"
APP_ID = "336F7D0E865F372DEA02E6B1867E8065"  # 应用唯一标识
APP_SECRET = "1A616768C006077954F4D62E4F189151"    # 应用密钥

# 配置文件路径
CONFIG_FILE = "config.ini"

# 默认服务器发送配置
DEFAULT_SERVER_URL = "http://your-server.com/api/qrcode"
DEFAULT_SERVER_ENABLED = True
DEFAULT_SERVER_TIMEOUT = 5

# 加载配置
def load_config():
    """从配置文件加载设置"""
    global SERVER_URL, SERVER_ENABLED, SERVER_TIMEOUT

    config = configparser.ConfigParser()

    if os.path.exists(CONFIG_FILE):
        try:
            config.read(CONFIG_FILE, encoding='utf-8')
            SERVER_URL = config.get('server', 'url', fallback=DEFAULT_SERVER_URL)
            SERVER_ENABLED = config.getboolean('server', 'enabled', fallback=DEFAULT_SERVER_ENABLED)
            SERVER_TIMEOUT = config.getint('server', 'timeout', fallback=DEFAULT_SERVER_TIMEOUT)
            print(f"配置已加载: URL={SERVER_URL}, 启用={SERVER_ENABLED}")
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            # 使用默认配置
            SERVER_URL = DEFAULT_SERVER_URL
            SERVER_ENABLED = DEFAULT_SERVER_ENABLED
            SERVER_TIMEOUT = DEFAULT_SERVER_TIMEOUT
    else:
        # 使用默认配置
        SERVER_URL = DEFAULT_SERVER_URL
        SERVER_ENABLED = DEFAULT_SERVER_ENABLED
        SERVER_TIMEOUT = DEFAULT_SERVER_TIMEOUT
        print("配置文件不存在，使用默认配置")

def save_config():
    """保存设置到配置文件"""
    config = configparser.ConfigParser()
    config['server'] = {
        'url': SERVER_URL,
        'enabled': str(SERVER_ENABLED),
        'timeout': str(SERVER_TIMEOUT)
    }

    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            config.write(f)
        if 'logger' in globals():
            logger.info("配置已保存")
        else:
            print("配置已保存")
    except Exception as e:
        if 'logger' in globals():
            logger.error(f"保存配置文件失败: {str(e)}")
        else:
            print(f"保存配置文件失败: {str(e)}")

# 先设置默认值，稍后加载配置
SERVER_URL = DEFAULT_SERVER_URL
SERVER_ENABLED = DEFAULT_SERVER_ENABLED
SERVER_TIMEOUT = DEFAULT_SERVER_TIMEOUT

ACCESS_TOKEN = None       # 全局票据
EXPIRE_TIME = 0           # 票据过期时间
TOKEN_EXPIRE_SECONDS = 7000  # 提前刷新时间（秒）

# 日志配置：按日期创建日志文件
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
log_filename = os.path.join(log_dir, datetime.now().strftime('%Y-%m-%d') + '.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
auto_scan_enabled = False
clipboard_observer = None
file_observer = None
processed_qrcodes = set()  # 避免重复处理
scanner_exclusive_mode = False  # 扫码器独占模式
scanner_queue = queue.Queue()  # 扫码器数据队列
scanner_thread = None  # 扫码器监听线程

def get_access_token():
    """获取并管理全局票据access_token，自动处理过期刷新"""
    global ACCESS_TOKEN, EXPIRE_TIME
    
    # 检查缓存的token是否有效
    if ACCESS_TOKEN and time.time() < EXPIRE_TIME:
        logger.info("使用缓存的access_token")
        return ACCESS_TOKEN
    
    # 构造请求URL
    url = f"http://{BASE_URL}/api/token?appid={APP_ID}&appsecret={APP_SECRET}"
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124 Safari/537.36"}
    
    try:
        logger.info(f"请求access_token: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text[:500]}")  # 打印完整响应内容
        
        # 尝试解析JSON
        try:
            data = response.json()
            logger.info(f"解析JSON成功: {data}")
        except json.JSONDecodeError:
            logger.error(f"响应不是有效的JSON格式: {response.text[:500]}")
            raise Exception(f"服务器返回非JSON数据: {response.text[:100]}")
        
        # 检查响应结构
        if not isinstance(data, dict):
            raise Exception(f"响应格式异常: {type(data)}")
        
        # 尝试提取access_token（兼容不同接口格式）
        if "access_token" in data:
            ACCESS_TOKEN = data["access_token"]
            expires_in = data.get("expires_in", 7200)
            # 处理异常有效期
            if expires_in <= 0:
                logger.warning("警告: 有效期异常，使用默认7200秒")
                expires_in = 7200
            # 设置过期时间（提前200秒刷新）
            EXPIRE_TIME = time.time() + expires_in - TOKEN_EXPIRE_SECONDS
            logger.info(f"获取成功，有效期: {expires_in}秒")
            return ACCESS_TOKEN
        else:
            # 尝试从其他可能的字段获取错误信息
            error_msg = data.get('message', data.get('msg', '未知错误'))
            error_code = data.get('code', '未知错误码')
            raise Exception(f"获取失败: {error_msg} (code: {error_code})")
            
    except Exception as e:
        logger.error(f"获取access_token异常: {str(e)}")
        raise Exception(f"获取access_token异常: {str(e)}")


def send_qrcode_to_server(qr_code):
    """发送二维码数据到服务器"""
    if not SERVER_ENABLED:
        return True

    try:
        # 构造发送数据
        data = {
            "qrcode": qr_code,
            "timestamp": int(time.time()),
            "device_id": "scanner_001",  # 可以根据需要修改设备ID
            "source": "usb_scanner"
        }

        headers = {
            "Content-Type": "application/json",
            "User-Agent": "QRCode-Scanner/1.0"
        }

        logger.info(f"发送二维码数据到服务器: {SERVER_URL}")
        logger.info(f"发送数据: {data}")

        response = requests.post(
            SERVER_URL,
            json=data,
            headers=headers,
            timeout=SERVER_TIMEOUT
        )

        logger.info(f"服务器响应状态码: {response.status_code}")
        logger.info(f"服务器响应内容: {response.text}")

        if response.status_code == 200:
            logger.info("二维码数据发送成功")
            return True
        else:
            logger.warning(f"服务器返回非200状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"发送二维码数据到服务器失败: {str(e)}")
        return False


def generate_sign(params):
    """生成签名（严格遵循文档规则：参数排序+拼接key=appid+MD5）"""
    # 1. 按参数名ASCII字典序排序
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    # 2. 拼接参数对（不编码，直接拼接）
    string_a = "&".join([f"{k}={v}" for k, v in sorted_params])
    # 3. 拼接appid（注意：key固定为'appid'）
    string_sign_temp = f"{string_a}&key={APP_ID}"
    # 4. 计算MD5并转大写
    sign = hashlib.md5(string_sign_temp.encode()).hexdigest().upper()

    logger.info("===== 签名生成详情 =====")
    logger.info(f"参与签名参数: {params}")
    logger.info(f"排序后参数: {sorted_params}")
    logger.info(f"拼接字符串: {string_sign_temp}")
    logger.info(f"生成签名: {sign}")
    return sign


def parse_qrcode(qr_code):
    """解析二维码获取人员编号"""
    access_token = get_access_token()
    # 构造请求参数（仅传递qrCode和签名）
    params = {
        "qrCode": qr_code,
        "sign": generate_sign({"qrCode": qr_code})
    }
    
    # 构造请求URL
    url = f"http://{BASE_URL}/api/common/infoqueryservice/parseqrcode?access_token={access_token}"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        logger.info(f"请求解析: {url}")
        logger.info(f"请求参数: {params}")
        response = requests.post(url, data=params, headers=headers, timeout=10)
        
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
        data = response.json()
        
        if data.get("code") == "0":
            per_code = data.get("data", {}).get("perCode")
            if not per_code:
                raise Exception("响应中缺少perCode字段")
            logger.info(f"解析成功，人员编号: {per_code}")
            return per_code
        else:
            error_msg = data.get('msg', '未知错误')
            error_code = data.get('code', '未知错误码')
            raise Exception(f"解析失败: {error_msg} (code: {error_code})")
            
    except Exception as e:
        logger.error(f"解析异常: {str(e)}")
        raise Exception(f"解析异常: {str(e)}")


def is_valid_qrcode(text):
    """判断是否为有效的二维码数据"""
    if not text or len(text) < 10:
        return False
    # 检查是否包含常见的二维码特征
    # 可以根据实际二维码格式调整正则表达式
    patterns = [
        r'^[A-Za-z0-9+/=]{20,}$',  # Base64编码
        r'^[0-9]{10,}$',  # 纯数字
        r'^[A-Za-z0-9]{10,}$',  # 字母数字组合
    ]
    for pattern in patterns:
        if re.match(pattern, text.strip()):
            return True
    return False


def is_scanner_data(text):
    """判断是否为扫码器数据（通常扫码器会快速输入并自动回车）"""
    if not text or len(text) < 5:
        return False
    # 扫码器通常输入的是纯数字或字母数字组合
    scanner_patterns = [
        r'^[0-9]{8,}$',  # 8位以上纯数字
        r'^[A-Za-z0-9]{8,}$',  # 8位以上字母数字组合
    ]
    for pattern in scanner_patterns:
        if re.match(pattern, text.strip()):
            return True
    return False


def scanner_listener():
    """扫码器监听线程"""
    buffer = ""
    last_input_time = time.time()
    
    def on_key_event(event):
        nonlocal buffer, last_input_time
        current_time = time.time()
        
        # 如果距离上次输入超过1秒，清空缓冲区（新的扫码开始）
        if current_time - last_input_time > 1:
            buffer = ""
        
        if event.event_type == keyboard.KEY_DOWN:
            if event.name == 'enter':
                # 扫码器输入完成
                if buffer.strip() and is_scanner_data(buffer.strip()):
                    logger.info(f"扫码器检测到数据: {buffer.strip()}")
                    scanner_queue.put(buffer.strip())
                buffer = ""
            elif event.name == 'backspace':
                # 退格键
                buffer = buffer[:-1] if buffer else ""
            elif len(event.name) == 1:
                # 普通字符
                buffer += event.name
            elif event.name.startswith('f') and event.name[1:].isdigit():
                # 功能键，忽略
                pass
            else:
                # 其他按键，忽略
                pass
            
            last_input_time = current_time
    
    # 注册键盘监听
    keyboard.hook(on_key_event)
    
    # 保持监听状态
    while scanner_exclusive_mode:
        time.sleep(0.1)
    
    # 取消监听
    keyboard.unhook_all()


def start_scanner_exclusive():
    """启动扫码器独占模式"""
    if not HAS_KEYBOARD:
        logger.error("keyboard未安装，扫码器独占功能不可用")
        return False
        
    global scanner_exclusive_mode, scanner_thread
    scanner_exclusive_mode = True
    scanner_thread = threading.Thread(target=scanner_listener, daemon=True)
    scanner_thread.start()
    logger.info("扫码器独占模式已启动")
    return True


def stop_scanner_exclusive():
    """停止扫码器独占模式"""
    global scanner_exclusive_mode
    scanner_exclusive_mode = False
    if scanner_thread:
        scanner_thread.join(timeout=1)
    logger.info("扫码器独占模式已停止")


def process_scanner_queue():
    """处理扫码器队列"""
    while scanner_exclusive_mode:
        try:
            # 非阻塞方式获取数据
            qr_data = scanner_queue.get_nowait()
            if qr_data and qr_data not in processed_qrcodes:
                logger.info(f"处理扫码器数据: {qr_data[:20]}...")
                auto_parse_qrcode(qr_data)
        except queue.Empty:
            pass
        time.sleep(0.1)


def auto_parse_qrcode(qr_code):
    """自动解析二维码"""
    if qr_code in processed_qrcodes:
        return
    processed_qrcodes.add(qr_code)

    # 首先发送原始二维码数据到服务器
    server_success = send_qrcode_to_server(qr_code)
    if server_success:
        logger.info(f"二维码数据已发送到服务器: {qr_code[:20]}...")
    else:
        logger.warning(f"二维码数据发送到服务器失败: {qr_code[:20]}...")

    try:
        result = parse_qrcode(qr_code)
        logger.info(f"自动解析成功 - 二维码: {qr_code[:20]}..., 人员编号: {result}")

        # 自动键盘输入结果
        if HAS_AUTOGUI:
            try:
                # 延迟0.5秒确保焦点准备就绪
                time.sleep(0.5)
                # 输入解析结果
                pyautogui.write(str(result))
                # 按回车键
                pyautogui.press('enter')
                logger.info(f"已自动输入结果: {result}")
            except Exception as e:
                logger.error(f"键盘输入失败: {str(e)}")
        else:
            logger.warning("pyautogui未安装，自动输入功能不可用")

        return result
    except Exception as e:
        logger.error(f"自动解析失败 - 二维码: {qr_code[:20]}..., 错误: {str(e)}")
        return None


def monitor_clipboard():
    """监控剪贴板变化"""
    if not HAS_CLIPBOARD:
        logger.warning("pyperclip未安装，剪贴板监控功能不可用")
        return
        
    last_clipboard = ""
    while auto_scan_enabled:
        try:
            current_clipboard = pyperclip.paste()
            if current_clipboard != last_clipboard and is_valid_qrcode(current_clipboard):
                logger.info(f"检测到剪贴板二维码: {current_clipboard[:20]}...")
                auto_parse_qrcode(current_clipboard)
            last_clipboard = current_clipboard
            time.sleep(1)  # 每秒检查一次
        except Exception as e:
            logger.error(f"剪贴板监控异常: {str(e)}")
            time.sleep(5)


class QRCodeFileHandler(FileSystemEventHandler):
    """文件监控处理器"""
    def on_created(self, event):
        if not event.is_directory and auto_scan_enabled:
            try:
                with open(event.src_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if is_valid_qrcode(content):
                        logger.info(f"检测到文件二维码: {content[:20]}...")
                        auto_parse_qrcode(content)
            except Exception as e:
                logger.error(f"文件监控异常: {str(e)}")


def start_auto_scan():
    """启动自动扫描"""
    global auto_scan_enabled, clipboard_observer, file_observer
    auto_scan_enabled = True
    
    # 启动剪贴板监控
    if HAS_CLIPBOARD:
        clipboard_thread = threading.Thread(target=monitor_clipboard, daemon=True)
        clipboard_thread.start()
        logger.info("剪贴板监控已启动")
    else:
        logger.warning("pyperclip未安装，跳过剪贴板监控")
    
    # 启动文件监控（监控logs目录）
    if HAS_WATCHDOG:
        try:
            file_observer = Observer()
            file_observer.schedule(QRCodeFileHandler(), log_dir, recursive=False)
            file_observer.start()
            logger.info("文件监控已启动")
        except Exception as e:
            logger.error(f"文件监控启动失败: {str(e)}")
    else:
        logger.warning("watchdog未安装，跳过文件监控")
    
    logger.info("自动扫描已启动")


def stop_auto_scan():
    """停止自动扫描"""
    global auto_scan_enabled, clipboard_observer, file_observer
    auto_scan_enabled = False
    
    if file_observer and HAS_WATCHDOG:
        file_observer.stop()
        file_observer.join()
    
    logger.info("自动扫描已停止")


def gui_main():
    def on_parse():
        qr_code = entry.get().strip()
        if not qr_code:
            messagebox.showerror("错误", "二维码数据不能为空")
            logger.error("错误: 二维码数据不能为空")
            return
        btn_parse.config(state=tk.DISABLED)
        result_var.set("")
        def worker():
            try:
                # 手动解析时也发送到服务器
                if server_enabled_var.get():
                    send_qrcode_to_server(qr_code)

                result = parse_qrcode(qr_code)
                result_var.set(f"人员编号: {result}")
                logger.info(f"解析结果: {result}")

                # 手动解析时也自动输入
                if HAS_AUTOGUI:
                    try:
                        time.sleep(0.5)
                        pyautogui.write(str(result))
                        pyautogui.press('enter')
                        logger.info(f"已自动输入结果: {result}")
                    except Exception as e:
                        logger.error(f"键盘输入失败: {str(e)}")
                else:
                    logger.warning("pyautogui未安装，自动输入功能不可用")

            except Exception as e:
                result_var.set(f"错误: {str(e)}")
                logger.error(f"错误: {str(e)}")
            finally:
                btn_parse.config(state=tk.NORMAL)
        threading.Thread(target=worker, daemon=True).start()

    def on_auto_scan_toggle():
        global auto_scan_enabled
        if not auto_scan_enabled:
            start_auto_scan()
            btn_auto.config(text="停止自动扫描")
            status_var.set("状态: 自动扫描中...")
        else:
            stop_auto_scan()
            btn_auto.config(text="启动自动扫描")
            status_var.set("状态: 已停止")

    def on_scanner_exclusive_toggle():
        global scanner_exclusive_mode
        if not scanner_exclusive_mode:
            if start_scanner_exclusive():
                btn_scanner.config(text="停止扫码器独占")
                scanner_status_var.set("扫码器状态: 独占模式已启动")
                # 启动扫码器队列处理
                threading.Thread(target=process_scanner_queue, daemon=True).start()
            else:
                messagebox.showerror("错误", "keyboard库未安装，扫码器独占功能不可用")
        else:
            stop_scanner_exclusive()
            btn_scanner.config(text="启动扫码器独占")
            scanner_status_var.set("扫码器状态: 已停止")

    def on_server_toggle():
        global SERVER_ENABLED
        SERVER_ENABLED = server_enabled_var.get()
        status_text = f"当前状态: {'启用' if SERVER_ENABLED else '禁用'}"
        server_status_var.set(status_text)
        if SERVER_ENABLED:
            logger.info("服务器发送功能已启用")
        else:
            logger.info("服务器发送功能已禁用")
        # 保存配置
        save_config()

    def on_server_url_change():
        global SERVER_URL
        new_url = server_url_var.get().strip()
        if new_url:
            SERVER_URL = new_url
            logger.info(f"服务器URL已更新: {SERVER_URL}")
            # 保存配置
            save_config()
        else:
            logger.warning("服务器URL不能为空")

    def on_closing():
        stop_auto_scan()
        stop_scanner_exclusive()
        if HAS_TRAY:
            root.withdraw()
            show_tray_icon()
        else:
            root.destroy()

    def show_tray_icon():
        if not HAS_TRAY:
            return
        def on_restore(icon, item):
            root.after(0, root.deiconify)
            icon.stop()
        # 创建托盘图标
        image = Image.new('RGB', (64, 64), color=(0, 120, 215))
        d = ImageDraw.Draw(image)
        d.rectangle([16, 16, 48, 48], fill=(255, 255, 255))
        icon = pystray.Icon("zy", image, "二维码解析", menu=pystray.Menu(
            pystray.MenuItem("显示窗口", on_restore),
            pystray.MenuItem("退出", lambda icon, item: (icon.stop(), root.quit()))
        ))
        icon.run()

    def start_auto_scan_on_startup():
        """程序启动后自动开启扫描"""
        root.after(1000, lambda: on_auto_scan_toggle())  # 延迟1秒启动

    root = tk.Tk()
    root.title("一卡通二维码解析 - 自动扫描版")
    root.geometry("500x550")  # 增加高度以容纳服务器配置
    root.resizable(False, False)

    # 主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 服务器配置变量
    server_enabled_var = tk.BooleanVar(value=SERVER_ENABLED)
    server_url_var = tk.StringVar(value=SERVER_URL)
    
    # 标题
    title_label = ttk.Label(main_frame, text="一卡通二维码解析系统", font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 手动解析区域
    manual_frame = ttk.LabelFrame(main_frame, text="手动解析", padding="10")
    manual_frame.pack(fill=tk.X, pady=(0, 10))
    
    ttk.Label(manual_frame, text="请输入二维码数据:").pack(anchor=tk.W)
    entry = ttk.Entry(manual_frame, width=50)
    entry.pack(fill=tk.X, pady=(5, 10))
    
    btn_parse = ttk.Button(manual_frame, text="解析", command=on_parse)
    btn_parse.pack(pady=(0, 5))
    
    # 结果区域
    result_var = tk.StringVar()
    result_label = ttk.Label(manual_frame, textvariable=result_var, foreground="blue")
    result_label.pack(pady=5)
    
    # 自动扫描区域
    auto_frame = ttk.LabelFrame(main_frame, text="自动扫描", padding="10")
    auto_frame.pack(fill=tk.X, pady=(0, 10))
    
    status_var = tk.StringVar(value="状态: 启动中...")
    ttk.Label(auto_frame, textvariable=status_var).pack(anchor=tk.W)
    
    btn_auto = ttk.Button(auto_frame, text="停止自动扫描", command=on_auto_scan_toggle)
    btn_auto.pack(pady=10)
    
    # 扫码器独占区域
    scanner_frame = ttk.LabelFrame(main_frame, text="扫码器独占", padding="10")
    scanner_frame.pack(fill=tk.X, pady=(0, 10))

    scanner_status_var = tk.StringVar(value="扫码器状态: 已停止")
    ttk.Label(scanner_frame, textvariable=scanner_status_var).pack(anchor=tk.W)

    btn_scanner = ttk.Button(scanner_frame, text="启动扫码器独占", command=on_scanner_exclusive_toggle)
    btn_scanner.pack(pady=10)

    # 服务器配置区域
    server_frame = ttk.LabelFrame(main_frame, text="服务器配置", padding="10")
    server_frame.pack(fill=tk.X, pady=(0, 10))

    # 启用服务器发送复选框
    server_checkbox = ttk.Checkbutton(
        server_frame,
        text="启用服务器发送",
        variable=server_enabled_var,
        command=on_server_toggle
    )
    server_checkbox.pack(anchor=tk.W, pady=(0, 5))

    # 服务器URL配置
    ttk.Label(server_frame, text="服务器URL:").pack(anchor=tk.W)
    server_url_entry = ttk.Entry(server_frame, textvariable=server_url_var, width=50)
    server_url_entry.pack(fill=tk.X, pady=(5, 5))
    server_url_entry.bind('<FocusOut>', lambda e: on_server_url_change())

    # 服务器状态显示
    server_status_text = f"当前状态: {'启用' if SERVER_ENABLED else '禁用'}"
    server_status_var = tk.StringVar(value=server_status_text)
    ttk.Label(server_frame, textvariable=server_status_var, foreground="blue").pack(anchor=tk.W, pady=(5, 0))
    
    # 功能状态提示
    status_text = "功能状态: "
    if not HAS_CLIPBOARD:
        status_text += "剪贴板监控不可用 "
    if not HAS_WATCHDOG:
        status_text += "文件监控不可用 "
    if not HAS_KEYBOARD:
        status_text += "扫码器独占不可用 "
    if not HAS_AUTOGUI:
        status_text += "自动输入不可用 "
    if not HAS_TRAY:
        status_text += "托盘功能不可用 "
    if status_text == "功能状态: ":
        status_text += "所有功能正常"
    
    status_label = ttk.Label(main_frame, text=status_text, foreground="gray", font=("Arial", 9))
    status_label.pack(anchor=tk.W, pady=5)
    
    # 说明文字
    info_text = """
功能说明：
1. 剪贴板监控：自动检测复制到剪贴板的二维码
2. 文件监控：监控logs目录下的新文件
3. 扫码器独占：独占扫码器输入，自动捕获扫码数据
4. 自动解析：检测到二维码后自动解析并记录日志
5. 重复过滤：避免重复处理相同的二维码
6. 程序启动后自动开启扫描模式
7. 自动输入：解析成功后自动键盘输入结果并回车
8. 服务器发送：识别到二维码数据时自动发送到指定服务器
    """
    info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT, foreground="gray")
    info_label.pack(anchor=tk.W, pady=10)
    
    # 关闭事件
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 托盘提示
    if not HAS_TRAY:
        ttk.Label(main_frame, text="未安装pystray/Pillow，无法最小化到托盘", foreground="red").pack()
    
    # 启动后自动开启扫描
    start_auto_scan_on_startup()
    
    root.mainloop()


if __name__ == "__main__":
    gui_main()