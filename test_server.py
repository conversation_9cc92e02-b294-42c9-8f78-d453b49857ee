#!/usr/bin/env python3
"""
简单的测试服务器，用于接收二维码数据
运行方式: python test_server.py
默认监听端口: 8080
接收URL: http://localhost:8080/api/qrcode
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import logging
from datetime import datetime
import urllib.parse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

class QRCodeHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/qrcode':
            try:
                # 获取请求内容长度
                content_length = int(self.headers.get('Content-Length', 0))
                
                # 读取请求体
                post_data = self.rfile.read(content_length)
                
                # 解析JSON数据
                try:
                    data = json.loads(post_data.decode('utf-8'))
                    logger.info(f"收到二维码数据: {data}")
                    
                    # 记录到文件
                    self.log_qrcode_data(data)
                    
                    # 返回成功响应
                    response = {
                        "status": "success",
                        "message": "二维码数据接收成功",
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                    
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                    self.send_error_response(400, "无效的JSON数据")
                    
            except Exception as e:
                logger.error(f"处理请求时发生错误: {e}")
                self.send_error_response(500, "服务器内部错误")
        else:
            self.send_error_response(404, "路径不存在")
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            # 返回简单的状态页面
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>二维码接收服务器</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>二维码接收服务器</h1>
                <p>服务器正在运行中...</p>
                <p>POST URL: /api/qrcode</p>
                <p>当前时间: {}</p>
            </body>
            </html>
            """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_error_response(404, "页面不存在")
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_error_response(self, code, message):
        """发送错误响应"""
        response = {
            "status": "error",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        self.send_response(code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def log_qrcode_data(self, data):
        """记录二维码数据到文件"""
        try:
            # 创建日志文件名
            log_filename = f"qrcode_received_{datetime.now().strftime('%Y-%m-%d')}.log"
            
            # 记录数据
            with open(log_filename, 'a', encoding='utf-8') as f:
                log_entry = {
                    "timestamp": datetime.now().isoformat(),
                    "data": data
                }
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"记录数据到文件失败: {e}")
    
    def log_message(self, format, *args):
        """重写日志方法，使用自定义格式"""
        logger.info(f"{self.address_string()} - {format % args}")

def run_server(port=8080):
    """启动服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, QRCodeHandler)
    
    logger.info(f"二维码接收服务器启动成功")
    logger.info(f"监听地址: http://localhost:{port}")
    logger.info(f"接收URL: http://localhost:{port}/api/qrcode")
    logger.info("按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
