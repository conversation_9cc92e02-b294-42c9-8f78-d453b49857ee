import requests
import hashlib
import time
import json

# 配置信息（请根据实际环境修改）
BASE_URL = "ymt.cslg.edu.cn:8081/easytong_app"
APP_ID = "336F7D0E865F372DEA02E6B1867E8065"  # 应用唯一标识
APP_SECRET = "1A616768C006077954F4D62E4F189151"    # 应用密钥

ACCESS_TOKEN = None       # 全局票据
EXPIRE_TIME = 0           # 票据过期时间
TOKEN_EXPIRE_SECONDS = 7000  # 提前刷新时间（秒）


def get_access_token():
    """获取并管理全局票据access_token，自动处理过期刷新"""
    global ACCESS_TOKEN, EXPIRE_TIME
    
    # 检查缓存的token是否有效
    if ACCESS_TOKEN and time.time() < EXPIRE_TIME:
        print("使用缓存的access_token")
        return ACCESS_TOKEN
    
    # 构造请求URL
    url = f"http://{BASE_URL}/api/token?appid={APP_ID}&appsecret={APP_SECRET}"
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124 Safari/537.36"}
    
    try:
        print(f"请求access_token: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}")  # 打印完整响应内容
        
        # 尝试解析JSON
        try:
            data = response.json()
            print(f"解析JSON成功: {data}")
        except json.JSONDecodeError:
            print(f"响应不是有效的JSON格式: {response.text[:500]}")
            raise Exception(f"服务器返回非JSON数据: {response.text[:100]}")
        
        # 检查响应结构
        if not isinstance(data, dict):
            raise Exception(f"响应格式异常: {type(data)}")
        
        # 尝试提取access_token（兼容不同接口格式）
        if "access_token" in data:
            ACCESS_TOKEN = data["access_token"]
            expires_in = data.get("expires_in", 7200)
            # 处理异常有效期
            if expires_in <= 0:
                print("警告: 有效期异常，使用默认7200秒")
                expires_in = 7200
            # 设置过期时间（提前200秒刷新）
            EXPIRE_TIME = time.time() + expires_in - TOKEN_EXPIRE_SECONDS
            print(f"获取成功，有效期: {expires_in}秒")
            return ACCESS_TOKEN
        else:
            # 尝试从其他可能的字段获取错误信息
            error_msg = data.get('message', data.get('msg', '未知错误'))
            error_code = data.get('code', '未知错误码')
            raise Exception(f"获取失败: {error_msg} (code: {error_code})")
            
    except Exception as e:
        raise Exception(f"获取access_token异常: {str(e)}")


def generate_sign(params):
    """生成签名（严格遵循文档规则：参数排序+拼接key=appid+MD5）"""
    # 1. 按参数名ASCII字典序排序
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    # 2. 拼接参数对（不编码，直接拼接）
    string_a = "&".join([f"{k}={v}" for k, v in sorted_params])
    # 3. 拼接appid（注意：key固定为'appid'）
    string_sign_temp = f"{string_a}&key={APP_ID}"
    # 4. 计算MD5并转大写
    sign = hashlib.md5(string_sign_temp.encode()).hexdigest().upper()
    
    print("\n===== 签名生成详情 =====")
    print(f"参与签名参数: {params}")
    print(f"排序后参数: {sorted_params}")
    print(f"拼接字符串: {string_sign_temp}")
    print(f"生成签名: {sign}")
    return sign


def parse_qrcode(qr_code):
    """解析二维码获取人员编号"""
    access_token = get_access_token()
    # 构造请求参数（仅传递qrCode和签名）
    params = {
        "qrCode": qr_code,
        "sign": generate_sign({"qrCode": qr_code})
    }
    
    # 构造请求URL
    url = f"http://{BASE_URL}/api/common/infoqueryservice/parseqrcode?access_token={access_token}"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        print(f"\n请求解析: {url}")
        print(f"请求参数: {params}")
        response = requests.post(url, data=params, headers=headers, timeout=10)
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        data = response.json()
        
        if data.get("code") == "0":
            per_code = data.get("data", {}).get("perCode")
            if not per_code:
                raise Exception("响应中缺少perCode字段")
            print(f"解析成功，人员编号: {per_code}")
            return per_code
        else:
            error_msg = data.get('msg', '未知错误')
            error_code = data.get('code', '未知错误码')
            raise Exception(f"解析失败: {error_msg} (code: {error_code})")
            
    except Exception as e:
        raise Exception(f"解析异常: {str(e)}")


def main():
    """主函数：获取二维码输入并解析"""
    print("=== 一卡通二维码解析程序 ===")
    print("说明: 输入二维码字符串，程序将解析并返回人员编号\n")
    
    # 获取用户输入
    qr_code = input("请输入二维码数据: ")
    if not qr_code:
        print("错误: 二维码数据不能为空")
        return
    
    # 执行解析
    try:
        result = parse_qrcode(qr_code)
        print("\n=== 解析结果 ===")
        print(f"人员编号: {result}")
    except Exception as e:
        print(f"\n错误: {str(e)}")


if __name__ == "__main__":
    main()