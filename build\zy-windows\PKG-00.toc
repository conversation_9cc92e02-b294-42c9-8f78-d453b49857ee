('D:\\MyData\\Desktop\\zy\\build\\zy-windows\\zy-windows.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\MyData\\Desktop\\zy\\build\\zy-windows\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\MyData\\Desktop\\zy\\build\\zy-windows\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\MyData\\Desktop\\zy\\build\\zy-windows\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\MyData\\Desktop\\zy\\build\\zy-windows\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\MyData\\Desktop\\zy\\build\\zy-windows\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\MyData\\Desktop\\zy\\build\\zy-windows\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('zy-windows', 'D:\\MyData\\Desktop\\zy\\zy-windows.py', 'PYSOURCE'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\python312.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\libssl-3-x64.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\DLLs\\tk86t.dll',
   'BINARY'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('base_library.zip',
   'D:\\MyData\\Desktop\\zy\\build\\zy-windows\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 True,
 [],
 None,
 None,
 None)
