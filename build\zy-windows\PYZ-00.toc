('D:\\MyData\\Desktop\\zy\\build\\zy-windows\\PYZ-00.pyz',
 [('PIL',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('keyboard',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\__init__.py',
   'PYMODULE'),
  ('keyboard._canonical_names',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\_canonical_names.py',
   'PYMODULE'),
  ('keyboard._darwinkeyboard',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\_darwinkeyboard.py',
   'PYMODULE'),
  ('keyboard._generic',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\_generic.py',
   'PYMODULE'),
  ('keyboard._keyboard_event',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\_keyboard_event.py',
   'PYMODULE'),
  ('keyboard._nixcommon',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\_nixcommon.py',
   'PYMODULE'),
  ('keyboard._nixkeyboard',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\_nixkeyboard.py',
   'PYMODULE'),
  ('keyboard._winkeyboard',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\keyboard\\_winkeyboard.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mouseinfo',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\opcode.py',
   'PYMODULE'),
  ('packaging',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pyautogui',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_osx',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_win',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_x11',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygetwindow',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('pymsgbox',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE'),
  ('pymsgbox._native_win',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE'),
  ('pyperclip',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyrect',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('pyscreeze',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE'),
  ('pystray',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\__init__.py',
   'PYMODULE'),
  ('pystray._appindicator',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_appindicator.py',
   'PYMODULE'),
  ('pystray._base',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_base.py',
   'PYMODULE'),
  ('pystray._darwin',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_darwin.py',
   'PYMODULE'),
  ('pystray._dummy',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_dummy.py',
   'PYMODULE'),
  ('pystray._gtk',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_gtk.py',
   'PYMODULE'),
  ('pystray._info',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_info.py',
   'PYMODULE'),
  ('pystray._util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_util\\__init__.py',
   'PYMODULE'),
  ('pystray._util.gtk',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_util\\gtk.py',
   'PYMODULE'),
  ('pystray._util.notify_dbus',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_util\\notify_dbus.py',
   'PYMODULE'),
  ('pystray._util.win32',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_util\\win32.py',
   'PYMODULE'),
  ('pystray._win32',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_win32.py',
   'PYMODULE'),
  ('pystray._xorg',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pystray\\_xorg.py',
   'PYMODULE'),
  ('pytweening',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\pytweening\\__init__.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\typing.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('watchdog',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\__init__.py',
   'PYMODULE'),
  ('watchdog.events',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\events.py',
   'PYMODULE'),
  ('watchdog.observers',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\__init__.py',
   'PYMODULE'),
  ('watchdog.observers.api',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\api.py',
   'PYMODULE'),
  ('watchdog.observers.fsevents',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\fsevents.py',
   'PYMODULE'),
  ('watchdog.observers.inotify',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\inotify.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_buffer',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\inotify_buffer.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_c',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\inotify_c.py',
   'PYMODULE'),
  ('watchdog.observers.kqueue',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\kqueue.py',
   'PYMODULE'),
  ('watchdog.observers.polling',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\polling.py',
   'PYMODULE'),
  ('watchdog.observers.read_directory_changes',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\read_directory_changes.py',
   'PYMODULE'),
  ('watchdog.observers.winapi',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\observers\\winapi.py',
   'PYMODULE'),
  ('watchdog.tricks',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\tricks\\__init__.py',
   'PYMODULE'),
  ('watchdog.utils',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\__init__.py',
   'PYMODULE'),
  ('watchdog.utils.bricks',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\bricks.py',
   'PYMODULE'),
  ('watchdog.utils.delayed_queue',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\delayed_queue.py',
   'PYMODULE'),
  ('watchdog.utils.dirsnapshot',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\dirsnapshot.py',
   'PYMODULE'),
  ('watchdog.utils.echo',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\echo.py',
   'PYMODULE'),
  ('watchdog.utils.event_debouncer',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\event_debouncer.py',
   'PYMODULE'),
  ('watchdog.utils.patterns',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\patterns.py',
   'PYMODULE'),
  ('watchdog.utils.platform',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\platform.py',
   'PYMODULE'),
  ('watchdog.utils.process_watcher',
   'D:\\MyData\\Desktop\\zy\\.venv\\Lib\\site-packages\\watchdog\\utils\\process_watcher.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.10-windows-x86_64-none\\Lib\\zipimport.py',
   'PYMODULE')])
