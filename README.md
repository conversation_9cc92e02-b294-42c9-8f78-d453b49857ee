# USB扫码器二维码识别与服务器发送系统

## 功能概述

这是一个功能完整的USB扫码器应用程序，能够自动识别二维码数据并同时发送到指定服务器。

## 主要功能

1. **USB扫码器监听**: 自动捕获USB扫码器输入的二维码数据
2. **自动解析**: 将二维码数据发送到易通平台进行解析，获取人员编号
3. **服务器发送**: 识别到二维码数据时，自动发送原始数据到您指定的服务器
4. **多种监控模式**:
   - 扫码器独占模式（推荐）
   - 剪贴板监控
   - 文件监控
5. **自动输入**: 解析成功后自动键盘输入结果
6. **配置持久化**: 服务器配置自动保存到配置文件

## 新增服务器发送功能

### 配置服务器

1. 启动程序后，在"服务器配置"区域：
   - 勾选"启用服务器发送"复选框
   - 在"服务器URL"输入框中填入您的服务器接收地址
   - 配置会自动保存到 `config.ini` 文件

### 数据发送格式

程序会以JSON格式发送数据到您的服务器：

```json
{
    "qrcode": "二维码原始数据",
    "timestamp": 1640995200,
    "device_id": "scanner_001",
    "source": "usb_scanner"
}
```

### 测试服务器

项目包含一个测试服务器 `test_server.py`，用于测试服务器发送功能：

```bash
# 启动测试服务器
python test_server.py

# 服务器将监听 http://localhost:8080/api/qrcode
```

然后在程序中将服务器URL设置为：`http://localhost:8080/api/qrcode`

## 使用方法

1. **启动程序**: 运行 `zy-windows.py` 或使用编译后的exe文件
2. **配置服务器**: 在服务器配置区域设置您的服务器URL
3. **启动扫码器独占**: 点击"启动扫码器独占"按钮
4. **开始扫码**: 使用USB扫码器扫描二维码

## 工作流程

1. USB扫码器扫描二维码
2. 程序自动捕获扫码数据
3. **同时执行两个操作**：
   - 发送原始二维码数据到您的服务器
   - 发送数据到易通平台解析获取人员编号
4. 自动键盘输入解析结果

## 配置文件

程序会自动创建 `config.ini` 配置文件：

```ini
[server]
url = http://your-server.com/api/qrcode
enabled = True
timeout = 5
```

## 日志记录

- 程序日志: `logs/YYYY-MM-DD.log`
- 测试服务器日志: `server.log`
- 接收数据日志: `qrcode_received_YYYY-MM-DD.log`

## 注意事项

1. 程序需要管理员权限运行（用于全局键盘监听）
2. 确保目标输入框获得焦点
3. 服务器发送是异步进行的，不会影响正常的解析流程
4. 如果服务器发送失败，程序会记录错误但继续正常工作

## 依赖库

程序会自动检测并提示缺失的依赖库：
- `requests`: HTTP请求
- `pyperclip`: 剪贴板监控
- `watchdog`: 文件监控
- `keyboard`: 键盘监听
- `pyautogui`: 自动输入
- `pystray`: 系统托盘
- `Pillow`: 图像处理