version = 1
revision = 2
requires-python = ">=3.12"

[[package]]
name = "altgraph"
version = "0.17.4"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/de/a8/7145824cf0b9e3c28046520480f207df47e927df83aa9555fb47f8505922/altgraph-0.17.4.tar.gz", hash = "sha256:1b5afbb98f6c4dcadb2e2ae6ab9fa994bbb8c1d75f4fa96d340f9437ae454406", size = 48418, upload-time = "2023-09-25T09:04:52.164Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4d/3f/3bc3f1d83f6e4a7fcb834d3720544ca597590425be5ba9db032b2bf322a2/altgraph-0.17.4-py2.py3-none-any.whl", hash = "sha256:642743b4750de17e655e6711601b077bc6598dbfa3ba5fa2b2a35ce12b508dff", size = 21212, upload-time = "2023-09-25T09:04:50.691Z" },
]

[[package]]
name = "certifi"
version = "2025.6.15"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/73/f7/f14b46d4bcd21092d7d3ccef689615220d8a08fb25e564b65d20738e672e/certifi-2025.6.15.tar.gz", hash = "sha256:d747aa5a8b9bbbb1bb8c22bb13e22bd1f18e9796defa16bab421f7f7a317323b", size = 158753, upload-time = "2025-06-15T02:45:51.329Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl", hash = "sha256:2e0c7ce7cb5d8f8634ca55d2ba7e6ec2689a2fd6537d8dec1296a477a4910057", size = 157650, upload-time = "2025-06-15T02:45:49.977Z" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e4/33/89c2ced2b67d1c2a61c19c6751aa8902d46ce3dacb23600a283619f5a12d/charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63", size = 126367, upload-time = "2025-05-02T08:34:42.01Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d7/a4/37f4d6035c89cac7930395a35cc0f1b872e652eaafb76a6075943754f095/charset_normalizer-3.4.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:0c29de6a1a95f24b9a1aa7aefd27d2487263f00dfd55a77719b530788f75cff7", size = 199936, upload-time = "2025-05-02T08:32:33.712Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ee/8a/1a5e33b73e0d9287274f899d967907cd0bf9c343e651755d9307e0dbf2b3/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:cddf7bd982eaa998934a91f69d182aec997c6c468898efe6679af88283b498d3", size = 143790, upload-time = "2025-05-02T08:32:35.768Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/66/52/59521f1d8e6ab1482164fa21409c5ef44da3e9f653c13ba71becdd98dec3/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:fcbe676a55d7445b22c10967bceaaf0ee69407fbe0ece4d032b6eb8d4565982a", size = 153924, upload-time = "2025-05-02T08:32:37.284Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/86/2d/fb55fdf41964ec782febbf33cb64be480a6b8f16ded2dbe8db27a405c09f/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:d41c4d287cfc69060fa91cae9683eacffad989f1a10811995fa309df656ec214", size = 146626, upload-time = "2025-05-02T08:32:38.803Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8c/73/6ede2ec59bce19b3edf4209d70004253ec5f4e319f9a2e3f2f15601ed5f7/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4e594135de17ab3866138f496755f302b72157d115086d100c3f19370839dd3a", size = 148567, upload-time = "2025-05-02T08:32:40.251Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/09/14/957d03c6dc343c04904530b6bef4e5efae5ec7d7990a7cbb868e4595ee30/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:cf713fe9a71ef6fd5adf7a79670135081cd4431c2943864757f0fa3a65b1fafd", size = 150957, upload-time = "2025-05-02T08:32:41.705Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0d/c8/8174d0e5c10ccebdcb1b53cc959591c4c722a3ad92461a273e86b9f5a302/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:a370b3e078e418187da8c3674eddb9d983ec09445c99a3a263c2011993522981", size = 145408, upload-time = "2025-05-02T08:32:43.709Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/58/aa/8904b84bc8084ac19dc52feb4f5952c6df03ffb460a887b42615ee1382e8/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:a955b438e62efdf7e0b7b52a64dc5c3396e2634baa62471768a64bc2adb73d5c", size = 153399, upload-time = "2025-05-02T08:32:46.197Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c2/26/89ee1f0e264d201cb65cf054aca6038c03b1a0c6b4ae998070392a3ce605/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:7222ffd5e4de8e57e03ce2cef95a4c43c98fcb72ad86909abdfc2c17d227fc1b", size = 156815, upload-time = "2025-05-02T08:32:48.105Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fd/07/68e95b4b345bad3dbbd3a8681737b4338ff2c9df29856a6d6d23ac4c73cb/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:bee093bf902e1d8fc0ac143c88902c3dfc8941f7ea1d6a8dd2bcb786d33db03d", size = 154537, upload-time = "2025-05-02T08:32:49.719Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/77/1a/5eefc0ce04affb98af07bc05f3bac9094513c0e23b0562d64af46a06aae4/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:dedb8adb91d11846ee08bec4c8236c8549ac721c245678282dcb06b221aab59f", size = 149565, upload-time = "2025-05-02T08:32:51.404Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/37/a0/2410e5e6032a174c95e0806b1a6585eb21e12f445ebe239fac441995226a/charset_normalizer-3.4.2-cp312-cp312-win32.whl", hash = "sha256:db4c7bf0e07fc3b7d89ac2a5880a6a8062056801b83ff56d8464b70f65482b6c", size = 98357, upload-time = "2025-05-02T08:32:53.079Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6c/4f/c02d5c493967af3eda9c771ad4d2bbc8df6f99ddbeb37ceea6e8716a32bc/charset_normalizer-3.4.2-cp312-cp312-win_amd64.whl", hash = "sha256:5a9979887252a82fefd3d3ed2a8e3b937a7a809f65dcb1e068b090e165bbe99e", size = 105776, upload-time = "2025-05-02T08:32:54.573Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ea/12/a93df3366ed32db1d907d7593a94f1fe6293903e3e92967bebd6950ed12c/charset_normalizer-3.4.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:926ca93accd5d36ccdabd803392ddc3e03e6d4cd1cf17deff3b989ab8e9dbcf0", size = 199622, upload-time = "2025-05-02T08:32:56.363Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/04/93/bf204e6f344c39d9937d3c13c8cd5bbfc266472e51fc8c07cb7f64fcd2de/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:eba9904b0f38a143592d9fc0e19e2df0fa2e41c3c3745554761c5f6447eedabf", size = 143435, upload-time = "2025-05-02T08:32:58.551Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/22/2a/ea8a2095b0bafa6c5b5a55ffdc2f924455233ee7b91c69b7edfcc9e02284/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:3fddb7e2c84ac87ac3a947cb4e66d143ca5863ef48e4a5ecb83bd48619e4634e", size = 153653, upload-time = "2025-05-02T08:33:00.342Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b6/57/1b090ff183d13cef485dfbe272e2fe57622a76694061353c59da52c9a659/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:98f862da73774290f251b9df8d11161b6cf25b599a66baf087c1ffe340e9bfd1", size = 146231, upload-time = "2025-05-02T08:33:02.081Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e2/28/ffc026b26f441fc67bd21ab7f03b313ab3fe46714a14b516f931abe1a2d8/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6c9379d65defcab82d07b2a9dfbfc2e95bc8fe0ebb1b176a3190230a3ef0e07c", size = 148243, upload-time = "2025-05-02T08:33:04.063Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c0/0f/9abe9bd191629c33e69e47c6ef45ef99773320e9ad8e9cb08b8ab4a8d4cb/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e635b87f01ebc977342e2697d05b56632f5f879a4f15955dfe8cef2448b51691", size = 150442, upload-time = "2025-05-02T08:33:06.418Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/67/7c/a123bbcedca91d5916c056407f89a7f5e8fdfce12ba825d7d6b9954a1a3c/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:1c95a1e2902a8b722868587c0e1184ad5c55631de5afc0eb96bc4b0d738092c0", size = 145147, upload-time = "2025-05-02T08:33:08.183Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ec/fe/1ac556fa4899d967b83e9893788e86b6af4d83e4726511eaaad035e36595/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:ef8de666d6179b009dce7bcb2ad4c4a779f113f12caf8dc77f0162c29d20490b", size = 153057, upload-time = "2025-05-02T08:33:09.986Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2b/ff/acfc0b0a70b19e3e54febdd5301a98b72fa07635e56f24f60502e954c461/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:32fc0341d72e0f73f80acb0a2c94216bd704f4f0bce10aedea38f30502b271ff", size = 156454, upload-time = "2025-05-02T08:33:11.814Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/92/08/95b458ce9c740d0645feb0e96cea1f5ec946ea9c580a94adfe0b617f3573/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:289200a18fa698949d2b39c671c2cc7a24d44096784e76614899a7ccf2574b7b", size = 154174, upload-time = "2025-05-02T08:33:13.707Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/be/8392efc43487ac051eee6c36d5fbd63032d78f7728cb37aebcc98191f1ff/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:4a476b06fbcf359ad25d34a057b7219281286ae2477cc5ff5e3f70a246971148", size = 149166, upload-time = "2025-05-02T08:33:15.458Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/44/96/392abd49b094d30b91d9fbda6a69519e95802250b777841cf3bda8fe136c/charset_normalizer-3.4.2-cp313-cp313-win32.whl", hash = "sha256:aaeeb6a479c7667fbe1099af9617c83aaca22182d6cf8c53966491a0f1b7ffb7", size = 98064, upload-time = "2025-05-02T08:33:17.06Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e9/b0/0200da600134e001d91851ddc797809e2fe0ea72de90e09bec5a2fbdaccb/charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl", hash = "sha256:aa6af9e7d59f9c12b33ae4e9450619cf2488e2bbe9b44030905877f0b2324980", size = 105641, upload-time = "2025-05-02T08:33:18.753Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/20/94/c5790835a017658cbfabd07f3bfb549140c3ac458cfc196323996b10095a/charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0", size = 52626, upload-time = "2025-05-02T08:34:40.053Z" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/f1/70/7703c29685631f5a7590aa73f1f1d3fa9a380e654b86af429e0934a32f7d/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9", size = 190490, upload-time = "2024-09-15T18:07:39.745Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", size = 70442, upload-time = "2024-09-15T18:07:37.964Z" },
]

[[package]]
name = "keyboard"
version = "0.13.5"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/79/75/c969f2258e908c39aadfc57d1cb78247dc49e6d36371bb3a48c194640c01/keyboard-0.13.5.zip", hash = "sha256:63ed83305955939ca5c9a73755e5cc43e8242263f5ad5fd3bb7e0b032f3d308b", size = 71798, upload-time = "2020-03-23T21:47:06.614Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/55/88/287159903c5b3fc6d47b651c7ab65a54dcf9c9916de546188a7f62870d6d/keyboard-0.13.5-py3-none-any.whl", hash = "sha256:8e9c2422f1217e0bd84489b9ecd361027cc78415828f4fe4f88dd4acd587947b", size = 58098, upload-time = "2020-03-23T21:47:05.023Z" },
]

[[package]]
name = "macholib"
version = "1.16.3"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "altgraph" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/95/ee/af1a3842bdd5902ce133bd246eb7ffd4375c38642aeb5dc0ae3a0329dfa2/macholib-1.16.3.tar.gz", hash = "sha256:07ae9e15e8e4cd9a788013d81f5908b3609aa76f9b1421bae9c4d7606ec86a30", size = 59309, upload-time = "2023-09-25T09:10:16.155Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d1/5d/c059c180c84f7962db0aeae7c3b9303ed1d73d76f2bfbc32bc231c8be314/macholib-1.16.3-py2.py3-none-any.whl", hash = "sha256:0e315d7583d38b8c77e815b1ecbdbf504a8258d8b3e17b61165c6feb60d18f2c", size = 38094, upload-time = "2023-09-25T09:10:14.188Z" },
]

[[package]]
name = "mouseinfo"
version = "0.1.3"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyperclip" },
    { name = "python3-xlib", marker = "sys_platform == 'linux'" },
    { name = "rubicon-objc", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/28/fa/b2ba8229b9381e8f6381c1dcae6f4159a7f72349e414ed19cfbbd1817173/MouseInfo-0.1.3.tar.gz", hash = "sha256:2c62fb8885062b8e520a3cce0a297c657adcc08c60952eb05bc8256ef6f7f6e7", size = 10850, upload-time = "2020-03-27T21:20:10.136Z" }

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a1/d4/1fc4078c65507b51b96ca8f8c3ba19e6a61c8253c72794544580a7b6c24d/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f", size = 165727, upload-time = "2025-04-19T11:48:59.673Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484", size = 66469, upload-time = "2025-04-19T11:48:57.875Z" },
]

[[package]]
name = "pefile"
version = "2023.2.7"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/c5/3b3c62223f72e2360737fd2a57c30e5b2adecd85e70276879609a7403334/pefile-2023.2.7.tar.gz", hash = "sha256:82e6114004b3d6911c77c3953e3838654b04511b8b66e8583db70c65998017dc", size = 74854, upload-time = "2023-02-07T12:23:55.958Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/55/26/d0ad8b448476d0a1e8d3ea5622dc77b916db84c6aa3cb1e1c0965af948fc/pefile-2023.2.7-py3-none-any.whl", hash = "sha256:da185cd2af68c08a6cd4481f7325ed600a88f6a813bad9dea07ab3ef73d8d8d6", size = 71791, upload-time = "2023-02-07T12:28:36.678Z" },
]

[[package]]
name = "pillow"
version = "11.2.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/af/cb/bb5c01fcd2a69335b86c22142b2bccfc3464087efb7fd382eee5ffc7fdf7/pillow-11.2.1.tar.gz", hash = "sha256:a64dd61998416367b7ef979b73d3a85853ba9bec4c2925f74e588879a58716b6", size = 47026707, upload-time = "2025-04-12T17:50:03.289Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c7/40/052610b15a1b8961f52537cc8326ca6a881408bc2bdad0d852edeb6ed33b/pillow-11.2.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:78afba22027b4accef10dbd5eed84425930ba41b3ea0a86fa8d20baaf19d807f", size = 3190185, upload-time = "2025-04-12T17:48:00.417Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e5/7e/b86dbd35a5f938632093dc40d1682874c33dcfe832558fc80ca56bfcb774/pillow-11.2.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:78092232a4ab376a35d68c4e6d5e00dfd73454bd12b230420025fbe178ee3b0b", size = 3030306, upload-time = "2025-04-12T17:48:02.391Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a4/5c/467a161f9ed53e5eab51a42923c33051bf8d1a2af4626ac04f5166e58e0c/pillow-11.2.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:25a5f306095c6780c52e6bbb6109624b95c5b18e40aab1c3041da3e9e0cd3e2d", size = 4416121, upload-time = "2025-04-12T17:48:04.554Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/62/73/972b7742e38ae0e2ac76ab137ca6005dcf877480da0d9d61d93b613065b4/pillow-11.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0c7b29dbd4281923a2bfe562acb734cee96bbb129e96e6972d315ed9f232bef4", size = 4501707, upload-time = "2025-04-12T17:48:06.831Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e4/3a/427e4cb0b9e177efbc1a84798ed20498c4f233abde003c06d2650a6d60cb/pillow-11.2.1-cp312-cp312-manylinux_2_28_aarch64.whl", hash = "sha256:3e645b020f3209a0181a418bffe7b4a93171eef6c4ef6cc20980b30bebf17b7d", size = 4522921, upload-time = "2025-04-12T17:48:09.229Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fe/7c/d8b1330458e4d2f3f45d9508796d7caf0c0d3764c00c823d10f6f1a3b76d/pillow-11.2.1-cp312-cp312-manylinux_2_28_x86_64.whl", hash = "sha256:b2dbea1012ccb784a65349f57bbc93730b96e85b42e9bf7b01ef40443db720b4", size = 4612523, upload-time = "2025-04-12T17:48:11.631Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b3/2f/65738384e0b1acf451de5a573d8153fe84103772d139e1e0bdf1596be2ea/pillow-11.2.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:da3104c57bbd72948d75f6a9389e6727d2ab6333c3617f0a89d72d4940aa0443", size = 4587836, upload-time = "2025-04-12T17:48:13.592Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6a/c5/e795c9f2ddf3debb2dedd0df889f2fe4b053308bb59a3cc02a0cd144d641/pillow-11.2.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:598174aef4589af795f66f9caab87ba4ff860ce08cd5bb447c6fc553ffee603c", size = 4669390, upload-time = "2025-04-12T17:48:15.938Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/96/ae/ca0099a3995976a9fce2f423166f7bff9b12244afdc7520f6ed38911539a/pillow-11.2.1-cp312-cp312-win32.whl", hash = "sha256:1d535df14716e7f8776b9e7fee118576d65572b4aad3ed639be9e4fa88a1cad3", size = 2332309, upload-time = "2025-04-12T17:48:17.885Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7c/18/24bff2ad716257fc03da964c5e8f05d9790a779a8895d6566e493ccf0189/pillow-11.2.1-cp312-cp312-win_amd64.whl", hash = "sha256:14e33b28bf17c7a38eede290f77db7c664e4eb01f7869e37fa98a5aa95978941", size = 2676768, upload-time = "2025-04-12T17:48:19.655Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/da/bb/e8d656c9543276517ee40184aaa39dcb41e683bca121022f9323ae11b39d/pillow-11.2.1-cp312-cp312-win_arm64.whl", hash = "sha256:21e1470ac9e5739ff880c211fc3af01e3ae505859392bf65458c224d0bf283eb", size = 2415087, upload-time = "2025-04-12T17:48:21.991Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/36/9c/447528ee3776e7ab8897fe33697a7ff3f0475bb490c5ac1456a03dc57956/pillow-11.2.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:fdec757fea0b793056419bca3e9932eb2b0ceec90ef4813ea4c1e072c389eb28", size = 3190098, upload-time = "2025-04-12T17:48:23.915Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b5/09/29d5cd052f7566a63e5b506fac9c60526e9ecc553825551333e1e18a4858/pillow-11.2.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:b0e130705d568e2f43a17bcbe74d90958e8a16263868a12c3e0d9c8162690830", size = 3030166, upload-time = "2025-04-12T17:48:25.738Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/71/5d/446ee132ad35e7600652133f9c2840b4799bbd8e4adba881284860da0a36/pillow-11.2.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:7bdb5e09068332578214cadd9c05e3d64d99e0e87591be22a324bdbc18925be0", size = 4408674, upload-time = "2025-04-12T17:48:27.908Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/69/5f/cbe509c0ddf91cc3a03bbacf40e5c2339c4912d16458fcb797bb47bcb269/pillow-11.2.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d189ba1bebfbc0c0e529159631ec72bb9e9bc041f01ec6d3233d6d82eb823bc1", size = 4496005, upload-time = "2025-04-12T17:48:29.888Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f9/b3/dd4338d8fb8a5f312021f2977fb8198a1184893f9b00b02b75d565c33b51/pillow-11.2.1-cp313-cp313-manylinux_2_28_aarch64.whl", hash = "sha256:191955c55d8a712fab8934a42bfefbf99dd0b5875078240943f913bb66d46d9f", size = 4518707, upload-time = "2025-04-12T17:48:31.874Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/13/eb/2552ecebc0b887f539111c2cd241f538b8ff5891b8903dfe672e997529be/pillow-11.2.1-cp313-cp313-manylinux_2_28_x86_64.whl", hash = "sha256:ad275964d52e2243430472fc5d2c2334b4fc3ff9c16cb0a19254e25efa03a155", size = 4610008, upload-time = "2025-04-12T17:48:34.422Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/72/d1/924ce51bea494cb6e7959522d69d7b1c7e74f6821d84c63c3dc430cbbf3b/pillow-11.2.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:750f96efe0597382660d8b53e90dd1dd44568a8edb51cb7f9d5d918b80d4de14", size = 4585420, upload-time = "2025-04-12T17:48:37.641Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/43/ab/8f81312d255d713b99ca37479a4cb4b0f48195e530cdc1611990eb8fd04b/pillow-11.2.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:fe15238d3798788d00716637b3d4e7bb6bde18b26e5d08335a96e88564a36b6b", size = 4667655, upload-time = "2025-04-12T17:48:39.652Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/94/86/8f2e9d2dc3d308dfd137a07fe1cc478df0a23d42a6c4093b087e738e4827/pillow-11.2.1-cp313-cp313-win32.whl", hash = "sha256:3fe735ced9a607fee4f481423a9c36701a39719252a9bb251679635f99d0f7d2", size = 2332329, upload-time = "2025-04-12T17:48:41.765Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6d/ec/1179083b8d6067a613e4d595359b5fdea65d0a3b7ad623fee906e1b3c4d2/pillow-11.2.1-cp313-cp313-win_amd64.whl", hash = "sha256:74ee3d7ecb3f3c05459ba95eed5efa28d6092d751ce9bf20e3e253a4e497e691", size = 2676388, upload-time = "2025-04-12T17:48:43.625Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/23/f1/2fc1e1e294de897df39fa8622d829b8828ddad938b0eaea256d65b84dd72/pillow-11.2.1-cp313-cp313-win_arm64.whl", hash = "sha256:5119225c622403afb4b44bad4c1ca6c1f98eed79db8d3bc6e4e160fc6339d66c", size = 2414950, upload-time = "2025-04-12T17:48:45.475Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c4/3e/c328c48b3f0ead7bab765a84b4977acb29f101d10e4ef57a5e3400447c03/pillow-11.2.1-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:8ce2e8411c7aaef53e6bb29fe98f28cd4fbd9a1d9be2eeea434331aac0536b22", size = 3192759, upload-time = "2025-04-12T17:48:47.866Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/0e/1c68532d833fc8b9f404d3a642991441d9058eccd5606eab31617f29b6d4/pillow-11.2.1-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:9ee66787e095127116d91dea2143db65c7bb1e232f617aa5957c0d9d2a3f23a7", size = 3033284, upload-time = "2025-04-12T17:48:50.189Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b7/cb/6faf3fb1e7705fd2db74e070f3bf6f88693601b0ed8e81049a8266de4754/pillow-11.2.1-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9622e3b6c1d8b551b6e6f21873bdcc55762b4b2126633014cea1803368a9aa16", size = 4445826, upload-time = "2025-04-12T17:48:52.346Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/94/8be03d50b70ca47fb434a358919d6a8d6580f282bbb7af7e4aa40103461d/pillow-11.2.1-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:63b5dff3a68f371ea06025a1a6966c9a1e1ee452fc8020c2cd0ea41b83e9037b", size = 4527329, upload-time = "2025-04-12T17:48:54.403Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fd/a4/bfe78777076dc405e3bd2080bc32da5ab3945b5a25dc5d8acaa9de64a162/pillow-11.2.1-cp313-cp313t-manylinux_2_28_aarch64.whl", hash = "sha256:31df6e2d3d8fc99f993fd253e97fae451a8db2e7207acf97859732273e108406", size = 4549049, upload-time = "2025-04-12T17:48:56.383Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/65/4d/eaf9068dc687c24979e977ce5677e253624bd8b616b286f543f0c1b91662/pillow-11.2.1-cp313-cp313t-manylinux_2_28_x86_64.whl", hash = "sha256:062b7a42d672c45a70fa1f8b43d1d38ff76b63421cbbe7f88146b39e8a558d91", size = 4635408, upload-time = "2025-04-12T17:48:58.782Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1d/26/0fd443365d9c63bc79feb219f97d935cd4b93af28353cba78d8e77b61719/pillow-11.2.1-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:4eb92eca2711ef8be42fd3f67533765d9fd043b8c80db204f16c8ea62ee1a751", size = 4614863, upload-time = "2025-04-12T17:49:00.709Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/49/65/dca4d2506be482c2c6641cacdba5c602bc76d8ceb618fd37de855653a419/pillow-11.2.1-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:f91ebf30830a48c825590aede79376cb40f110b387c17ee9bd59932c961044f9", size = 4692938, upload-time = "2025-04-12T17:49:02.946Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b3/92/1ca0c3f09233bd7decf8f7105a1c4e3162fb9142128c74adad0fb361b7eb/pillow-11.2.1-cp313-cp313t-win32.whl", hash = "sha256:e0b55f27f584ed623221cfe995c912c61606be8513bfa0e07d2c674b4516d9dd", size = 2335774, upload-time = "2025-04-12T17:49:04.889Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a5/ac/77525347cb43b83ae905ffe257bbe2cc6fd23acb9796639a1f56aa59d191/pillow-11.2.1-cp313-cp313t-win_amd64.whl", hash = "sha256:36d6b82164c39ce5482f649b437382c0fb2395eabc1e2b1702a6deb8ad647d6e", size = 2681895, upload-time = "2025-04-12T17:49:06.635Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/67/32/32dc030cfa91ca0fc52baebbba2e009bb001122a1daa8b6a79ad830b38d3/pillow-11.2.1-cp313-cp313t-win_arm64.whl", hash = "sha256:225c832a13326e34f212d2072982bb1adb210e0cc0b153e688743018c94a2681", size = 2417234, upload-time = "2025-04-12T17:49:08.399Z" },
]

[[package]]
name = "pyautogui"
version = "0.9.54"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "mouseinfo" },
    { name = "pygetwindow" },
    { name = "pymsgbox" },
    { name = "pyobjc-core", marker = "sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", marker = "sys_platform == 'darwin'" },
    { name = "pyscreeze" },
    { name = "python3-xlib", marker = "sys_platform == 'linux'" },
    { name = "pytweening" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/65/ff/cdae0a8c2118a0de74b6cf4cbcdcaf8fd25857e6c3f205ce4b1794b27814/PyAutoGUI-0.9.54.tar.gz", hash = "sha256:dd1d29e8fd118941cb193f74df57e5c6ff8e9253b99c7b04f39cfc69f3ae04b2", size = 61236, upload-time = "2023-05-24T20:11:32.972Z" }

[[package]]
name = "pygetwindow"
version = "0.0.9"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyrect" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e1/70/c7a4f46dbf06048c6d57d9489b8e0f9c4c3d36b7479f03c5ca97eaa2541d/PyGetWindow-0.0.9.tar.gz", hash = "sha256:17894355e7d2b305cd832d717708384017c1698a90ce24f6f7fbf0242dd0a688", size = 9699, upload-time = "2020-10-04T02:12:50.806Z" }

[[package]]
name = "pyinstaller"
version = "6.14.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "altgraph" },
    { name = "macholib", marker = "sys_platform == 'darwin'" },
    { name = "packaging" },
    { name = "pefile", marker = "sys_platform == 'win32'" },
    { name = "pyinstaller-hooks-contrib" },
    { name = "pywin32-ctypes", marker = "sys_platform == 'win32'" },
    { name = "setuptools" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/9f/4a/d66d3a9c34349d73eb099401060e2591da8ccc5ed427e54fff3961302513/pyinstaller-6.14.1.tar.gz", hash = "sha256:35d5c06a668e21f0122178dbf20e40fd21012dc8f6170042af6050c4e7b3edca", size = 4284317, upload-time = "2025-06-08T18:45:46.367Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/43/f6/fa56e547fe849db4b8da0acaad6101a6382c18370c7e0f378a1cf0ea89f0/pyinstaller-6.14.1-py3-none-macosx_10_13_universal2.whl", hash = "sha256:da559cfe4f7a20a7ebdafdf12ea2a03ea94d3caa49736ef53ee2c155d78422c9", size = 999937, upload-time = "2025-06-08T18:44:26.429Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/af/a6/a2814978f47ae038b1ce112717adbdcfd8dfb9504e5c52437902331cde1a/pyinstaller-6.14.1-py3-none-manylinux2014_aarch64.whl", hash = "sha256:f040d1e3d42af3730104078d10d4a8ca3350bd1c78de48f12e1b26f761e0cbc3", size = 719569, upload-time = "2025-06-08T18:44:30.948Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/35/f0/86391a4c0f558aef43a7dac8f678d46f4e5b84bd133308e3ea81f7384ab9/pyinstaller-6.14.1-py3-none-manylinux2014_i686.whl", hash = "sha256:7b8813fb2d5a82ef4ceffc342ed9a11a6fc1ef21e68e833dbd8fedb8a188d3f5", size = 729824, upload-time = "2025-06-08T18:44:34.983Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e5/88/446814e335d937406e6e1ae4a77ed922b8eea8b90f3aaf69427a16b58ed2/pyinstaller-6.14.1-py3-none-manylinux2014_ppc64le.whl", hash = "sha256:e2cfdbc6dd41d19872054fc233da18856ec422a7fdea899b6985ae04f980376a", size = 727937, upload-time = "2025-06-08T18:44:38.954Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c6/0f/5aa891c61d303ad4a794b7e2f864aacf64fe0f6f5559e2aec0f742595fad/pyinstaller-6.14.1-py3-none-manylinux2014_s390x.whl", hash = "sha256:a4d53b3ecb5786b097b79bda88c4089186fc1498ef7eaa6cee57599ae459241e", size = 724762, upload-time = "2025-06-08T18:44:42.768Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c5/92/e32ec0a1754852a8ed5a60f6746c6483e3da68aee97d314f3a3a99e0ed9e/pyinstaller-6.14.1-py3-none-manylinux2014_x86_64.whl", hash = "sha256:c48dd257f77f61ebea2d1fdbaf11243730f2271873c88d3b5ecb7869525d3bcb", size = 724957, upload-time = "2025-06-08T18:44:46.829Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c3/66/1260f384e47bf939f6238f791d4cda7edb94771d2fa0a451e0edb21ac9c7/pyinstaller-6.14.1-py3-none-musllinux_1_1_aarch64.whl", hash = "sha256:5b05cbb2ffc033b4681268159b82bac94b875475c339603c7e605f00a73c8746", size = 724132, upload-time = "2025-06-08T18:44:51.081Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d2/8b/8570ab94ec07e0b2b1203f45840353ee76aa067a2540c97da43d43477b26/pyinstaller-6.14.1-py3-none-musllinux_1_1_x86_64.whl", hash = "sha256:d5fd73757c8ea9adb2f9c1f81656335ba9890029ede3031835d768fde36e89f0", size = 723847, upload-time = "2025-06-08T18:44:54.896Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d5/43/6c68dc9e53b09ff948d6e46477932b387832bbb920c48061d734ef089368/pyinstaller-6.14.1-py3-none-win32.whl", hash = "sha256:547f7a93592e408cbfd093ce9fd9631215387dab0dbf3130351d3b0b1186a534", size = 1299744, upload-time = "2025-06-08T18:45:00.781Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7c/dd/bb8d5bcb0592f7f5d454ad308051d00ed34f8b08d5003400b825cfe35513/pyinstaller-6.14.1-py3-none-win_amd64.whl", hash = "sha256:0794290b4b56ef9d35858334deb29f36ec1e1f193b0f825212a0aa5a1bec5a2f", size = 1357625, upload-time = "2025-06-08T18:45:06.826Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/89/57/8a8979737980e50aa5031b77318ce783759bf25be2956317f2e1d7a65a09/pyinstaller-6.14.1-py3-none-win_arm64.whl", hash = "sha256:d9d99695827f892cb19644106da30681363e8ff27b8326ac8416d62890ab9c74", size = 1298607, upload-time = "2025-06-08T18:45:12.766Z" },
]

[[package]]
name = "pyinstaller-hooks-contrib"
version = "2025.5"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "packaging" },
    { name = "setuptools" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/5f/ff/e3376595935d5f8135964d2177cd3e3e0c1b5a6237497d9775237c247a5d/pyinstaller_hooks_contrib-2025.5.tar.gz", hash = "sha256:707386770b8fe066c04aad18a71bc483c7b25e18b4750a756999f7da2ab31982", size = 163124, upload-time = "2025-06-08T18:47:53.26Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0c/2c/b4d317534e17dd1df95c394d4b37febb15ead006a1c07c2bb006481fb5e7/pyinstaller_hooks_contrib-2025.5-py3-none-any.whl", hash = "sha256:ebfae1ba341cb0002fb2770fad0edf2b3e913c2728d92df7ad562260988ca373", size = 437246, upload-time = "2025-06-08T18:47:51.516Z" },
]

[[package]]
name = "pymsgbox"
version = "1.0.9"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/7d/ff/4c6f31a4f08979f12a663f2aeb6c8b765d3bd592e66eaaac445f547bb875/PyMsgBox-1.0.9.tar.gz", hash = "sha256:2194227de8bff7a3d6da541848705a155dcbb2a06ee120d9f280a1d7f51263ff", size = 18829, upload-time = "2020-10-11T01:51:43.227Z" }

[[package]]
name = "pyobjc"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-accessibility", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-accounts", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-addressbook" },
    { name = "pyobjc-framework-adservices", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-adsupport", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-applescriptkit" },
    { name = "pyobjc-framework-applescriptobjc", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-applicationservices" },
    { name = "pyobjc-framework-apptrackingtransparency", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-audiovideobridging", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-authenticationservices", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-automaticassessmentconfiguration", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-automator" },
    { name = "pyobjc-framework-avfoundation", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-avkit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-avrouting", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-backgroundassets", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-browserenginekit", marker = "platform_release >= '23.4'" },
    { name = "pyobjc-framework-businesschat", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-calendarstore", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-callkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-carbon" },
    { name = "pyobjc-framework-cfnetwork" },
    { name = "pyobjc-framework-cinematic", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-classkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-cloudkit", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-collaboration", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-colorsync", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-contacts", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-contactsui", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coreaudiokit" },
    { name = "pyobjc-framework-corebluetooth", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-coredata" },
    { name = "pyobjc-framework-corehaptics", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-corelocation", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-coremedia", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-coremediaio", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-coremidi" },
    { name = "pyobjc-framework-coreml", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-coremotion", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-coreservices" },
    { name = "pyobjc-framework-corespotlight", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-coretext" },
    { name = "pyobjc-framework-corewlan", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-cryptotokenkit", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-datadetection", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-devicecheck", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-devicediscoveryextension", marker = "platform_release >= '24.0'" },
    { name = "pyobjc-framework-dictionaryservices", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-discrecording" },
    { name = "pyobjc-framework-discrecordingui" },
    { name = "pyobjc-framework-diskarbitration" },
    { name = "pyobjc-framework-dvdplayback" },
    { name = "pyobjc-framework-eventkit", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-exceptionhandling" },
    { name = "pyobjc-framework-executionpolicy", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-extensionkit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-externalaccessory", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-fileprovider", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-fileproviderui", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-findersync", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-fsevents", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-fskit", marker = "platform_release >= '24.4'" },
    { name = "pyobjc-framework-gamecenter", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-gamecontroller", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-gamekit", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-gameplaykit", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-healthkit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-imagecapturecore", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-inputmethodkit", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-installerplugins" },
    { name = "pyobjc-framework-instantmessage", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-intents", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-intentsui", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-iobluetooth" },
    { name = "pyobjc-framework-iobluetoothui" },
    { name = "pyobjc-framework-iosurface", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-ituneslibrary", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-kernelmanagement", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-latentsemanticmapping" },
    { name = "pyobjc-framework-launchservices" },
    { name = "pyobjc-framework-libdispatch", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-libxpc", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-linkpresentation", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-localauthentication", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-localauthenticationembeddedui", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mailkit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mapkit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaaccessibility", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaextension", marker = "platform_release >= '24.0'" },
    { name = "pyobjc-framework-medialibrary", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaplayer", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-mediatoolbox", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-metal", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-metalfx", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-metalkit", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-metalperformanceshaders", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-metalperformanceshadersgraph", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-metrickit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mlcompute", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-modelio", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-multipeerconnectivity", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-naturallanguage", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-netfs", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-network", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-networkextension", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-notificationcenter", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-opendirectory", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-osakit" },
    { name = "pyobjc-framework-oslog", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-passkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-pencilkit", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-phase", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-photos", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-photosui", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-preferencepanes" },
    { name = "pyobjc-framework-pushkit", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-quartz" },
    { name = "pyobjc-framework-quicklookthumbnailing", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-replaykit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-safariservices", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-safetykit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-scenekit", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-screencapturekit", marker = "platform_release >= '21.4'" },
    { name = "pyobjc-framework-screensaver" },
    { name = "pyobjc-framework-screentime", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-scriptingbridge", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-searchkit" },
    { name = "pyobjc-framework-security" },
    { name = "pyobjc-framework-securityfoundation" },
    { name = "pyobjc-framework-securityinterface" },
    { name = "pyobjc-framework-securityui", marker = "platform_release >= '24.4'" },
    { name = "pyobjc-framework-sensitivecontentanalysis", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-servicemanagement", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-sharedwithyou", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-sharedwithyoucore", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-shazamkit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-social", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-soundanalysis", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-speech", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-spritekit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-storekit", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-symbols", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-syncservices" },
    { name = "pyobjc-framework-systemconfiguration" },
    { name = "pyobjc-framework-systemextensions", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-threadnetwork", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-uniformtypeidentifiers", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-usernotifications", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-usernotificationsui", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-videosubscriberaccount", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-videotoolbox", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-virtualization", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-vision", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-webkit" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/db/5e/16bc372806790d295c76b5c7851767cc9ee3787b3e581f5d7cc44158e4e0/pyobjc-11.1.tar.gz", hash = "sha256:a71b14389657811d658526ba4d5faba4ef7eadbddcf9fe8bf4fb3a6261effba3", size = 11161, upload-time = "2025-06-14T20:56:32.819Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a9/32/ad08b45fc0ad9850054ffe66fb0cb2ff7af3d2007c192dda14cf9a3ea893/pyobjc-11.1-py3-none-any.whl", hash = "sha256:903f822cba40be53d408b8eaf834514937ec0b4e6af1c5ecc24fcb652812dd85", size = 4164, upload-time = "2025-06-14T20:44:42.659Z" },
]

[[package]]
name = "pyobjc-core"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e8/e9/0b85c81e2b441267bca707b5d89f56c2f02578ef8f3eafddf0e0c0b8848c/pyobjc_core-11.1.tar.gz", hash = "sha256:b63d4d90c5df7e762f34739b39cc55bc63dbcf9fb2fb3f2671e528488c7a87fe", size = 974602, upload-time = "2025-06-14T20:56:34.189Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c0/09/e83228e878e73bf756749939f906a872da54488f18d75658afa7f1abbab1/pyobjc_core-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:765b97dea6b87ec4612b3212258024d8496ea23517c95a1c5f0735f96b7fd529", size = 677985, upload-time = "2025-06-14T20:44:48.375Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c5/24/12e4e2dae5f85fd0c0b696404ed3374ea6ca398e7db886d4f1322eb30799/pyobjc_core-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:18986f83998fbd5d3f56d8a8428b2f3e0754fd15cef3ef786ca0d29619024f2c", size = 676431, upload-time = "2025-06-14T20:44:49.908Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f7/79/031492497624de4c728f1857181b06ce8c56444db4d49418fa459cba217c/pyobjc_core-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:8849e78cfe6595c4911fbba29683decfb0bf57a350aed8a43316976ba6f659d2", size = 719330, upload-time = "2025-06-14T20:44:51.621Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ed/7d/6169f16a0c7ec15b9381f8bf33872baf912de2ef68d96c798ca4c6ee641f/pyobjc_core-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:8cb9ed17a8d84a312a6e8b665dd22393d48336ea1d8277e7ad20c19a38edf731", size = 667203, upload-time = "2025-06-14T20:44:53.262Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/49/0f/f5ab2b0e57430a3bec9a62b6153c0e79c05a30d77b564efdb9f9446eeac5/pyobjc_core-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f2455683e807f8541f0d83fbba0f5d9a46128ab0d5cc83ea208f0bec759b7f96", size = 708807, upload-time = "2025-06-14T20:44:54.851Z" },
]

[[package]]
name = "pyobjc-framework-accessibility"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/b4/10c16e9d48568a68da2f61866b19468d4ac7129c377d4b1333ee936ae5d0/pyobjc_framework_accessibility-11.1.tar.gz", hash = "sha256:c0fa5f1e00906ec002f582c7d3d80463a46d19f672bf5ec51144f819eeb40656", size = 45098, upload-time = "2025-06-14T20:56:35.287Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5d/bd/087d511e0ea356434399609a38e8819978943cbeaca3ca7cc5f35c93d0b2/pyobjc_framework_accessibility-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a049b63b32514da68aaaeef0d6c00a125e0618e4042aa6dbe3867b74fb2a8b2b", size = 11158, upload-time = "2025-06-14T20:44:59.032Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0e/1e/4095d683954401d5f7926827fd09f4d399a8923e0e66d386a8903c0950e0/pyobjc_framework_accessibility-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fd5a03b731d1a2bbb2bf706b58889a5e82df82ac69210ec3245c7dc69e42a63a", size = 11177, upload-time = "2025-06-14T20:45:00.111Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/28/7f/63d88c16e87f07b7bfff2adc7e74dcb2739cc1aed2110d29489514c05afa/pyobjc_framework_accessibility-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3496c55569a421ef3c98ea66fc0ebaf68c686ede5b26db0fdcb0b0ad4191a20b", size = 11356, upload-time = "2025-06-14T20:45:01.183Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ee/bd/7062e8670f7636aed8d61bde807a458a21962585e9d352cd576631a5eb96/pyobjc_framework_accessibility-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:7c4124981a5d84b71464babb4babfbeb5bfab145bc75b6f3577bd046a9579226", size = 11246, upload-time = "2025-06-14T20:45:02.21Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/73/79/66e1500a49203931d5b18fd4ae2f40139c27063e6724536d803d07b5bc14/pyobjc_framework_accessibility-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:ea98239e339136e3d20d753afe7908006cf29567ba39b8e83ceda7c221e6aad1", size = 11438, upload-time = "2025-06-14T20:45:02.923Z" },
]

[[package]]
name = "pyobjc-framework-accounts"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/12/45/ca21003f68ad0f13b5a9ac1761862ad2ddd83224b4314a2f7d03ca437c8d/pyobjc_framework_accounts-11.1.tar.gz", hash = "sha256:384fec156e13ff75253bb094339013f4013464f6dfd47e2f7de3e2ae7441c030", size = 17086, upload-time = "2025-06-14T20:56:36.035Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6d/db/fa1c4a964fb9f390af8fce1d82c053f9d4467ffe6acdaab464bb3220e673/pyobjc_framework_accounts-11.1-py2.py3-none-any.whl", hash = "sha256:9c3fe342be7b8e73cba735e5a38affbe349cf8bc19091aa4fd788eabf2074b72", size = 5117, upload-time = "2025-06-14T20:45:04.696Z" },
]

[[package]]
name = "pyobjc-framework-addressbook"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/eb/d3/f5bb5c72be5c6e52224f43e23e5a44e86d2c35ee9af36939e5514c6c7a0f/pyobjc_framework_addressbook-11.1.tar.gz", hash = "sha256:ce2db3be4a3128bf79d5c41319a6d16b73754785ce75ac694d0d658c690922fc", size = 97609, upload-time = "2025-06-14T20:56:37.324Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c2/de/e1ba5f113c05b543a097040add795fa4b85fdd5ad850b56d83cd6ce8afff/pyobjc_framework_addressbook-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:fb3d0a710f8342a0c63a8e4caf64a044b4d7e42d6d242c8e1b54470238b938cb", size = 13173, upload-time = "2025-06-14T20:45:07.755Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/59/53/a0487a0fbc9134e69e29f18334d0b610c44578d753e8264ea1ac649f2839/pyobjc_framework_addressbook-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:411adf4874cc4343f2928a26fe4cb3673d2f5f73365b45cd3650aa7304a45e24", size = 13188, upload-time = "2025-06-14T20:45:08.811Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/81/07/1ca336107358ad526394a720598b8549f613ef1797350c764535f26e47bc/pyobjc_framework_addressbook-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6735f297f0e5fd109fa77ca90cace57eb2e10eb65e3c15ccd249df2228030d3b", size = 13358, upload-time = "2025-06-14T20:45:09.877Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/96/f7/c5ca9d90b2f6c6c04df8c61f788c5667467d1c63b8ccb85521eab9d463f7/pyobjc_framework_addressbook-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e4004bdf134a069c58d91b231cbeb9e0adad26a73d2689015baaf6a98c411c54", size = 13228, upload-time = "2025-06-14T20:45:10.601Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6a/14/275315178d6fa10ebc51d9713580ed53b6df3b3773600cfaef6ca4aa9baf/pyobjc_framework_addressbook-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6bc42832e85f418a9f978b7e001e219faf52cbb279a0df185115cd4292c381cb", size = 13396, upload-time = "2025-06-14T20:45:11.822Z" },
]

[[package]]
name = "pyobjc-framework-adservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/2a/3f/af76eab6eee0a405a4fdee172e7181773040158476966ecd757b0a98bfc5/pyobjc_framework_adservices-11.1.tar.gz", hash = "sha256:44c72f8163705c9aa41baca938fdb17dde257639e5797e6a5c3a2b2d8afdade9", size = 12473, upload-time = "2025-06-14T20:56:38.147Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8e/11/a63a171ce86c25a6ae85ebff6a9ab92b0d0cb1fd66ddc7d7b0d803f36191/pyobjc_framework_adservices-11.1-py2.py3-none-any.whl", hash = "sha256:1744f59a75b2375e139c39f3e85658e62cd10cc0f12b158a80421f18734e9ffc", size = 3474, upload-time = "2025-06-14T20:45:13.263Z" },
]

[[package]]
name = "pyobjc-framework-adsupport"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/7f/03/9c51edd964796a97def4e1433d76a128dd7059b685fb4366081bf4e292ba/pyobjc_framework_adsupport-11.1.tar.gz", hash = "sha256:78b9667c275785df96219d205bd4309731869c3298d0931e32aed83bede29096", size = 12556, upload-time = "2025-06-14T20:56:38.741Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7d/b8/ad895efb24311cab2b9d6f7f7f6a833b7f354f80fec606e6c7893da9349b/pyobjc_framework_adsupport-11.1-py2.py3-none-any.whl", hash = "sha256:c3e009612778948910d3a7135b9d77b9b7c06aab29d40957770834c083acf825", size = 3387, upload-time = "2025-06-14T20:45:14.394Z" },
]

[[package]]
name = "pyobjc-framework-applescriptkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/bc/63/1bcfcdca53bf5bba3a7b4d73d24232ae1721a378a32fd4ebc34a35549df2/pyobjc_framework_applescriptkit-11.1.tar.gz", hash = "sha256:477707352eaa6cc4a5f8c593759dc3227a19d5958481b1482f0d59394a4601c3", size = 12392, upload-time = "2025-06-14T20:56:39.331Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c6/0e/68ac4ce71e613697a087c262aefacc9ed54eaf0cf1d9ffcd89134bfdab9b/pyobjc_framework_applescriptkit-11.1-py2.py3-none-any.whl", hash = "sha256:e22cbc9d1a25a4a713f21aa94dd017c311186b02062fc7ffbde3009495fb0067", size = 4334, upload-time = "2025-06-14T20:45:15.205Z" },
]

[[package]]
name = "pyobjc-framework-applescriptobjc"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a3/27/687b55b575367df045879b786f358355e40e41f847968e557d0718a6c4a4/pyobjc_framework_applescriptobjc-11.1.tar.gz", hash = "sha256:c8a0ec975b64411a4f16a1280c5ea8dbe949fd361e723edd343102f0f95aba6e", size = 12445, upload-time = "2025-06-14T20:56:39.976Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2d/33/ceb6a512b41fbf3458b9a281997ebb3056cc354981215261f0a2bf7d15d6/pyobjc_framework_applescriptobjc-11.1-py2.py3-none-any.whl", hash = "sha256:ac22526fd1f0a3b07ac1d77f90046b77f10ec9549182114f2428ee1e96d3de2b", size = 4433, upload-time = "2025-06-14T20:45:16.061Z" },
]

[[package]]
name = "pyobjc-framework-applicationservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coretext" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/be/3f/b33ce0cecc3a42f6c289dcbf9ff698b0d9e85f5796db2e9cb5dadccffbb9/pyobjc_framework_applicationservices-11.1.tar.gz", hash = "sha256:03fcd8c0c600db98fa8b85eb7b3bc31491701720c795e3f762b54e865138bbaf", size = 224842, upload-time = "2025-06-14T20:56:40.648Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/38/ec/46a5c710e2d7edf55105223c34fed5a7b7cc7aba7d00a3a7b0405d6a2d1a/pyobjc_framework_applicationservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f4a85ccd78bab84f7f05ac65ff9be117839dfc09d48c39edd65c617ed73eb01c", size = 31056, upload-time = "2025-06-14T20:45:18.925Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c4/06/c2a309e6f37bfa73a2a581d3301321b2033e25b249e2a01e417a3c34e799/pyobjc_framework_applicationservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:385a89f4d0838c97a331e247519d9e9745aa3f7427169d18570e3c664076a63c", size = 31072, upload-time = "2025-06-14T20:45:19.707Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/5f/357bf498c27f1b4d48385860d8374b2569adc1522aabe32befd77089c070/pyobjc_framework_applicationservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f480fab20f3005e559c9d06c9a3874a1f1c60dde52c6d28a53ab59b45e79d55f", size = 31335, upload-time = "2025-06-14T20:45:20.462Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ab/b6/797fdd81399fe8251196f29a621ba3f3f04d5c579d95fd304489f5558202/pyobjc_framework_applicationservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e8dee91c6a14fd042f98819dc0ac4a182e0e816282565534032f0e544bfab143", size = 31196, upload-time = "2025-06-14T20:45:21.555Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/68/45/47eba8d7cdf16d778240ed13fb405e8d712464170ed29d0463363a695194/pyobjc_framework_applicationservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a0ce40a57a9b993793b6f72c4fd93f80618ef54a69d76a1da97b8360a2f3ffc5", size = 31446, upload-time = "2025-06-14T20:45:22.313Z" },
]

[[package]]
name = "pyobjc-framework-apptrackingtransparency"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/49/68/7aa3afffd038dd6e5af764336bca734eb910121013ca71030457b61e5b99/pyobjc_framework_apptrackingtransparency-11.1.tar.gz", hash = "sha256:796cc5f83346c10973806cfb535d4200b894a5d2626ff2eeb1972d594d14fed4", size = 13135, upload-time = "2025-06-14T20:56:41.494Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/21/37/22cc0293c911a98a49c5fc007b968d82797101dd06e89c4c3266564ff443/pyobjc_framework_apptrackingtransparency-11.1-py2.py3-none-any.whl", hash = "sha256:e25c3eae25d24ee8b523b7ecc4d2b07af37c7733444b80c4964071dea7b0cb19", size = 3862, upload-time = "2025-06-14T20:45:23.851Z" },
]

[[package]]
name = "pyobjc-framework-audiovideobridging"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/c3/25/6c5a7b1443d30139cc722029880284ea9dfa575f0436471b9364fcd499f5/pyobjc_framework_audiovideobridging-11.1.tar.gz", hash = "sha256:12756b3aa35083b8ad5c9139b6a0e2f4792e217096b5bf6b702d499038203991", size = 72913, upload-time = "2025-06-14T20:56:42.128Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1d/69/3e8e3da4db835168d18155a2c90fcca441047fc9c2e021d2ea01b4c6eb8c/pyobjc_framework_audiovideobridging-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:591e80ff6973ea51a12f7c1a2e3fd59496633a51d5a1bf73f4fb989a43e23681", size = 11032, upload-time = "2025-06-14T20:45:26.196Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0b/93/cf38f503f378e224a57f99f8ca7f044f2690221dc8deaf49b305a6ee439a/pyobjc_framework_audiovideobridging-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:30a12be3784f41e1c6b5ef532c08e73bae7071d9a036b26b1e36b919ee5b6f57", size = 11043, upload-time = "2025-06-14T20:45:27.214Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/cf/ed/b2804e0415429292fd2f891f29e57b5008a2ecebb7de83aa9b78281e9284/pyobjc_framework_audiovideobridging-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3bef4383dc9233dbd9efc3817ce9c8fe8670c61d21a94de3c149e7f460245792", size = 11217, upload-time = "2025-06-14T20:45:27.892Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a4/34/6a92d1795bf246222a6e3c993ae12f95b3453c1777ee564ef685b7c31260/pyobjc_framework_audiovideobridging-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:6159b94448af08c9b119eb6ecf3fdbc2b3348ad66fb99586f991939779e412ec", size = 11075, upload-time = "2025-06-14T20:45:28.939Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/33/7d/975b7d24b103e015f2289cc160ea01b47b43a242b6f69f0b23a19e38b8bc/pyobjc_framework_audiovideobridging-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e466561bd9eb77be050aabead6ad7313a480d05389d9892e1db2cbc06ce1f475", size = 11248, upload-time = "2025-06-14T20:45:29.959Z" },
]

[[package]]
name = "pyobjc-framework-authenticationservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/8f/b7/3e9ad0ed3625dc02e495615ea5dbf55ca95cbd25b3e31f25092f5caad640/pyobjc_framework_authenticationservices-11.1.tar.gz", hash = "sha256:8fd801cdb53d426b4e678b0a8529c005d0c44f5a17ccd7052a7c3a1a87caed6a", size = 115266, upload-time = "2025-06-14T20:56:42.889Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7e/2d/cbb5e88c3713fb68cda7d76d37737076c1653bf1ac95418c30d4b614f4be/pyobjc_framework_authenticationservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6655dd53d9135ef85265a4297da5e7459ed7836973f2796027fdfbfd7f08e433", size = 20385, upload-time = "2025-06-14T20:45:33.359Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/53/ac/cfd8aed9fba6974f291b3beb198c7270e4a3cae9f1ff9600bd0e4c904ae9/pyobjc_framework_authenticationservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:364035d265129192e6906f7a94cbdf714d737b6b9f20e56bfe74d0007c8761b1", size = 20401, upload-time = "2025-06-14T20:45:34.114Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/58/37/949c2f06ea52d976ff7c2c52a58504456ae4cc4f6c681e65ea9fa448a676/pyobjc_framework_authenticationservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e92bf7e829229fbecba4f7f649d3ae38760cf25aa9e909c0e737b1945f36b62d", size = 20636, upload-time = "2025-06-14T20:45:34.875Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/15/75/6372808569c763ea00ba393d4eaee5cf4f73fd4fd5b222042e1c0d2aac65/pyobjc_framework_authenticationservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:60bf585e561d885cc88a21713ef2db259baf6434ce7116f82265a0c727f29dba", size = 20574, upload-time = "2025-06-14T20:45:35.947Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/74/25/996581a175ce0394ee1abb76c4798478bc0ef32f55a78d4b49079b24fd78/pyobjc_framework_authenticationservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f19ea757ecfda6ac929559c779c3afb001855dd5e41e4acc4c42343c7d912da6", size = 20822, upload-time = "2025-06-14T20:45:36.702Z" },
]

[[package]]
name = "pyobjc-framework-automaticassessmentconfiguration"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/3d/39/d4c94e0245d290b83919854c4f205851cc0b2603f843448fdfb8e74aad71/pyobjc_framework_automaticassessmentconfiguration-11.1.tar.gz", hash = "sha256:70eadbf8600101901a56fcd7014d8941604e14f3b3728bc4fb0178a9a9420032", size = 24933, upload-time = "2025-06-14T20:56:43.984Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5e/e0/5a67f8ee0393447ca8251cbd06788cb7f3a1f4b9b052afd2e1b2cdfcb504/pyobjc_framework_automaticassessmentconfiguration-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:55d1684dd676730fb1afbc7c67e0669e3a7159f18c126fea7453fe6182c098f9", size = 9193, upload-time = "2025-06-14T20:45:40.52Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/58/04/e2fb203d36b7ec96b06ef26cb44b833d64195435bc5d879987238111b524/pyobjc_framework_automaticassessmentconfiguration-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fbcbe406c2a02d632885f6b23285c259b715f019b938d666cc554a66ecf5f9c3", size = 9199, upload-time = "2025-06-14T20:45:41.742Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/03/d7/bd947463be8b6f1512a99cb605a57a52f960bb70da060e21a23131a55386/pyobjc_framework_automaticassessmentconfiguration-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e5fa297c7d4db225f75e5d11121fa68e0956c104e14b24250a52157a180e5f6c", size = 9359, upload-time = "2025-06-14T20:45:42.444Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/bf/72/b4674dc09acc106be130737b0d18f17ba0b5b72728d52bc951511d4067c0/pyobjc_framework_automaticassessmentconfiguration-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4b11c33fb6f6092b9e1fb63747f2402f516b7ff0f815be4ece4625f2a2ec954f", size = 9262, upload-time = "2025-06-14T20:45:43.14Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c7/09/05c9cd16cf2374c38c6dbc3b43e84de5fa7435e557985f4403ac7dea33fd/pyobjc_framework_automaticassessmentconfiguration-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:378d233879bb011ed9d0bcf1b0e3c048fb756023d0f6819e997f62acc2c32bc3", size = 9397, upload-time = "2025-06-14T20:45:43.834Z" },
]

[[package]]
name = "pyobjc-framework-automator"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/63/9f/097ed9f4de9e9491a1b08bb7d85d35a95d726c9e9f5f5bf203b359a436b6/pyobjc_framework_automator-11.1.tar.gz", hash = "sha256:9b46c55a4f9ae2b3c39ff560f42ced66bdd18c093188f0b5fc4060ad911838e4", size = 201439, upload-time = "2025-06-14T20:56:44.767Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0e/1e/3ed1df2168e596151da2329258951dae334e194d7de3b117c7e29a768ffc/pyobjc_framework_automator-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:af5941f8d90167244209b352512b7779e5590d17dc1e703e087a6cfe79ee3d64", size = 10029, upload-time = "2025-06-14T20:45:46.823Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/25/ed/a92cea530aac0cf08287321ec8123e8447f93461521f46bb329058b322eb/pyobjc_framework_automator-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3458f836671ea922ad0771f617c927e9c52841c0a6e71b4a5a9dbb438736c207", size = 10040, upload-time = "2025-06-14T20:45:47.549Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e9/30/c284723dd871e59756d24ddb4a9728db87b9e1b1610d22f3f60ad9de8b45/pyobjc_framework_automator-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:203b888152a78b39a8c67be663ff78a749ebff208ce993b4419fc4409faa1fda", size = 10186, upload-time = "2025-06-14T20:45:48.265Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/89/ac/a1e4e318bb972c2e62bdd215490bc4c24cdfac881e3ade5660d2b1412779/pyobjc_framework_automator-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:651760236cb2d2481faa5afb66da97054850d34fdbebc5e4ee2f83a683a8be10", size = 10086, upload-time = "2025-06-14T20:45:49.294Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7b/9c/ffcc59f5ff3aadfba6b94ba641c668bca10e0612f8754c25753f0a12f41a/pyobjc_framework_automator-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:112815d2e1b6002b4f9bc644bdae6b02257d249145c79346d7b8bb11e6f76b03", size = 10239, upload-time = "2025-06-14T20:45:50.018Z" },
]

[[package]]
name = "pyobjc-framework-avfoundation"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/3c/1f/90cdbce1d3b4861cbb17c12adf57daeec32477eb1df8d3f9ab8551bdadfb/pyobjc_framework_avfoundation-11.1.tar.gz", hash = "sha256:6663056cc6ca49af8de6d36a7fff498f51e1a9a7f1bde7afba718a8ceaaa7377", size = 832178, upload-time = "2025-06-14T20:56:46.329Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/43/30/d5d03dd4a508bdaa2156ff379e9e109020de23cbb6316c5865d341aa6db1/pyobjc_framework_avfoundation-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:94f065db4e87b1baebb5cf9f464cf9d82c5f903fff192001ebc974d9e3132c7e", size = 70746, upload-time = "2025-06-14T20:45:53.253Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3f/8c/b8ced7700b0e931dc37d14b05e2bead28d2598c887832b3d697da55b1845/pyobjc_framework_avfoundation-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:e204d155a09c186601490e4402dcffb2845a5831079e389b47bd6a341fe5ee63", size = 70773, upload-time = "2025-06-14T20:45:54.059Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d6/4c/086f4713793aaabdb5134debbf1fdc6c7d4ef5a32a6b35529e2e69580ec8/pyobjc_framework_avfoundation-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:dd3965aad0b236b8ac12f216d688c1a22b963f63e7e4fdb7107dd6790e80ee12", size = 71352, upload-time = "2025-06-14T20:45:54.871Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a6/5f/d5c4b9812e22c6fdf234421f131efae7c3137e838bb9df9be8bb45cde97b/pyobjc_framework_avfoundation-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:1ab2108b652496b13b9758c295f0f6de53b6d12125cf574ddae84ce28044bce1", size = 71208, upload-time = "2025-06-14T20:45:56.057Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/29/d0/dec23e1745a81f5576cba577fa7218d665f36250a8507eaaa83a84579abf/pyobjc_framework_avfoundation-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5dd6ac6a57f86b7ed5ac0a965ce54328f6ce77816b4a1fbf0d85c06fb251867a", size = 71680, upload-time = "2025-06-14T20:45:57.091Z" },
]

[[package]]
name = "pyobjc-framework-avkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/61/ff/9f41f2b8de786871184b48c4e5052cb7c9fcc204e7fee06687fa32b08bed/pyobjc_framework_avkit-11.1.tar.gz", hash = "sha256:d948204a7b94e0e878b19a909f9b33342e19d9ea519571d66a21fce8f72e3263", size = 46825, upload-time = "2025-06-14T20:56:47.494Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b2/2f/6ec6a4ec7eb9ca329f36bbd2a51750fe5064d44dd437d8615abb7121ec93/pyobjc_framework_avkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ef9cd9fe37c6199bfde7ee5cd6e76ede23a6797932882785c53ef3070e209afb", size = 11539, upload-time = "2025-06-14T20:46:00.375Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/16/c8/6f0131f62f70e201a605b762cc05804b01fd493a7f21824d714140b7fd99/pyobjc_framework_avkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c5810b349745078ef8b4a562e85afe40de3245127f633d8cabe98aeca765c7fc", size = 11551, upload-time = "2025-06-14T20:46:01.071Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a9/e6/a5bfa072393416c940a35b182457fee4779cf2f010c5772a9b690522afef/pyobjc_framework_avkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:023b1cdb78c3aa5873d8abe69697396872b47278208991ec5e5aea4464309b01", size = 11749, upload-time = "2025-06-14T20:46:01.785Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/35/15/fdb3c2dbce6cc7236bced3874fe5cf4b32b3af786447aae033bb1831f5e9/pyobjc_framework_avkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a6b418603fc270a8e63c2a5efffa753704fd14bf8bca0657901c49a7cc9b22b5", size = 11587, upload-time = "2025-06-14T20:46:02.6Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fc/2e/a311d27ac6785bfe51e6276ad326be90ca928cb07d73fc4fb8e8857f7ce0/pyobjc_framework_avkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3a5f22bc4f4b0b82c8039d37996882bf4a38f509963d1afa3275a45ddd4a0b00", size = 11766, upload-time = "2025-06-14T20:46:03.29Z" },
]

[[package]]
name = "pyobjc-framework-avrouting"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/cf/42/94bc18b968a4ee8b6427257f907ffbfc97f8ba6a6202953da149b649d638/pyobjc_framework_avrouting-11.1.tar.gz", hash = "sha256:7db1291d9f53cc58d34b2a826feb721a85f50ceb5e71952e8762baacd3db3fc0", size = 21069, upload-time = "2025-06-14T20:56:48.57Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/01/17/ce199bc7fb3ba1f7b0474554bd71d1bdd3d5a141e1d9722ff9f46c104e1d/pyobjc_framework_avrouting-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dc309e175abf3961f933f8b341c0504b17f4717931242ebb121a83256b8b5c13", size = 8212, upload-time = "2025-06-14T20:46:06.17Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/72/39/5c550da37c6d5a18a9b4a7d0fd6f7396ca8fbbee8cfccf82f3298e0f86b3/pyobjc_framework_avrouting-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f52f9d62a3c8485b5687187ea58d905d7edccac9941c444b4add8129841cd031", size = 8230, upload-time = "2025-06-14T20:46:06.919Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6b/ee/fec9662a0f7756a3440cd1c31be8c3a2db98d9b88210e46ca76b36e151ca/pyobjc_framework_avrouting-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6a7b335161d327792f42054acb3ff415f7778e1492582df8e91b8609b4b02244", size = 8383, upload-time = "2025-06-14T20:46:07.593Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/41/34/31b10439741980c9f226623ec9cee9649a8ac34a81efd1ad26f72a7d02da/pyobjc_framework_avrouting-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:120c9d65d4f9047b9921f8dced0b4f26d799156bc08ff7e3974217cd036b1bfc", size = 8269, upload-time = "2025-06-14T20:46:08.284Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1d/7b/9fed48dcc1b94fa20d5435c352bea2ce431541e43b43fb720dcb43fc3d16/pyobjc_framework_avrouting-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9aa9b0a7ae7ee5874e7d92bebefca4525d5cf1f0aa1f50e78e558984a39cad2e", size = 8410, upload-time = "2025-06-14T20:46:09.321Z" },
]

[[package]]
name = "pyobjc-framework-backgroundassets"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/08/76/21e1632a212f997d7a5f26d53eb997951978916858039b79f43ebe3d10b2/pyobjc_framework_backgroundassets-11.1.tar.gz", hash = "sha256:2e14b50539d96d5fca70c49f21b69fdbad81a22549e3630f5e4f20d5c0204fc2", size = 24803, upload-time = "2025-06-14T20:56:49.566Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ad/77/a6ad2df35fd71b3c26f52698d25174899ba1be134766022f5bf804ebf12d/pyobjc_framework_backgroundassets-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:13bf451c59b409b6ce1ac0e717a970a1b03bca7a944a7f19219da0d46ab7c561", size = 9707, upload-time = "2025-06-14T20:46:12.88Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1d/7f/ed035866ab6c0573c445a9ed1ceb0912119866c130df7684a2332642520e/pyobjc_framework_backgroundassets-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:708466d847a479e1798f31c59fbc5307473d03fa1083f40cfcaa18fd31819c40", size = 9722, upload-time = "2025-06-14T20:46:13.574Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/05/e9/15f540b4bee160fd4b66f294ee4cd326aaa94632bcbee12d4b2448bb74ee/pyobjc_framework_backgroundassets-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2484a2f9c87e8cae2fc375a39d68ea7ff02e4fb786e4afe88237c51fd5e78ec9", size = 9899, upload-time = "2025-06-14T20:46:14.277Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9b/aa/17dd9b9def7d9d29c1ee14e1b3100e0bf9dbc5fdd4a12d1bd4c6e79b46d2/pyobjc_framework_backgroundassets-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a72536ed18cf2462085bbb2184d0a3eecf9b97669c0ef4db45418555a609b534", size = 9774, upload-time = "2025-06-14T20:46:14.957Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5a/de/852cb10bb11a0e88d2422f24c2bdb8eeeabf9c0a400e1cba03a7af351dca/pyobjc_framework_backgroundassets-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a4db45048d1021900be5b03136b927773820bcbb40d623aeac54712e1c86d6f6", size = 9948, upload-time = "2025-06-14T20:46:15.655Z" },
]

[[package]]
name = "pyobjc-framework-browserenginekit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/30/75/087270d9f81e913b57c7db58eaff8691fa0574b11faf9302340b3b8320f1/pyobjc_framework_browserenginekit-11.1.tar.gz", hash = "sha256:918440cefb10480024f645169de3733e30ede65e41267fa12c7b90c264a0a479", size = 31944, upload-time = "2025-06-14T20:56:50.195Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/89/90/a50bb66a5e041ace99b6c8b1df43b38d5f2e1bf771f57409e4aebf1dfae5/pyobjc_framework_browserenginekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9b815b167533015d62832b956e9cfb962bd2026f5a4ccd66718cf3bb2e15ab27", size = 11115, upload-time = "2025-06-14T20:46:19.401Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/44/0a/3cbfc8ca58ed9aeef7498f318ad209164903e64eba1ea94a661a59ee67e6/pyobjc_framework_browserenginekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:dfe469f8eb1313ea0cbe0616cd3bbc56f62bdd8a683c959819ef01d7e9ac0de7", size = 11134, upload-time = "2025-06-14T20:46:20.445Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4d/d6/013d10fc2ad2c7095e1b61b1b3db2c38aec403784f81b70237d11ba615a8/pyobjc_framework_browserenginekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f3332ffa9ae74cc6633fd17f6d998ac77b8939abbe9ecf95ae56df200ee93853", size = 11322, upload-time = "2025-06-14T20:46:21.476Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/63/ba/59869b4f500a1f7edf6eb84b6e018df37655b0b6b96fc6e2d00dfa3b648d/pyobjc_framework_browserenginekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:c3195c4fb3b84150fac6dd18ce318eaae17f246f98678825397ed80d6da3c371", size = 11170, upload-time = "2025-06-14T20:46:22.52Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c2/9a/0e75c06c0f48c368b7eb2d5aa6bde780106fad080fd74a76e109eef6afc6/pyobjc_framework_browserenginekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1f4cce594a94d0bc0a020122153f8149c16578fa4761b0e27d868c013f76214c", size = 11369, upload-time = "2025-06-14T20:46:23.235Z" },
]

[[package]]
name = "pyobjc-framework-businesschat"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/85/be/9d9d9d9383c411a58323ea510d768443287ca21610af652b815b3205ea80/pyobjc_framework_businesschat-11.1.tar.gz", hash = "sha256:69589d2f0cb4e7892e5ecc6aed79b1abd1ec55c099a7faacae6a326bc921259d", size = 12698, upload-time = "2025-06-14T20:56:51.173Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/87/a4/5b8bb268b263678c0908cdaa8bed2534a6caac5862d05236f6c361d130ba/pyobjc_framework_businesschat-11.1-py2.py3-none-any.whl", hash = "sha256:7fdc1219b988ce3ae896bffd01f547c06cec3b4e4b2d0aa04d251444d7f1c2db", size = 3458, upload-time = "2025-06-14T20:46:24.651Z" },
]

[[package]]
name = "pyobjc-framework-calendarstore"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/41/df/7ca8ee65b16d5fc862d7e8664289472eed918cf4d76921de6bdaa1461c65/pyobjc_framework_calendarstore-11.1.tar.gz", hash = "sha256:858ee00e6a380d9c086c2d7db82c116a6c406234038e0ec8fc2ad02e385dc437", size = 68215, upload-time = "2025-06-14T20:56:51.799Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c7/94/69cb863bd88349df0f6cf491fd3ca4d674816c4d66270f9e2620cc6e16ed/pyobjc_framework_calendarstore-11.1-py2.py3-none-any.whl", hash = "sha256:bf066e17392c978becf17a61863eb81727bf593a2bfdab261177126072557e24", size = 5265, upload-time = "2025-06-14T20:46:25.457Z" },
]

[[package]]
name = "pyobjc-framework-callkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/51/d5/4f0b62ab35be619e8c8d96538a03cf56fde6fd53540e1837e0fa588b3f6c/pyobjc_framework_callkit-11.1.tar.gz", hash = "sha256:b84d5ea38dff0cbe0754f5f9f6f33c742e216f12e7166179a8ec2cf4b0bfca94", size = 46648, upload-time = "2025-06-14T20:56:52.579Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/2a/209572a6dba6768a57667e1f87a83ce8cadf18de5d6b1a91b95ce548d0f8/pyobjc_framework_callkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:554e09ca3dab44d93a89927d9e300f004d2ef0db020b10425a4622b432e7b684", size = 11269, upload-time = "2025-06-14T20:46:28.164Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8f/74/b0a22adb7ebcd0b81c24ed6e49d3df3b84f73192b667ebd90cb1b6eba917/pyobjc_framework_callkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fc5e638ddbc9dd3e9993205d2b077f5db41b6cd4e97b9c5592b7249575f23f04", size = 11284, upload-time = "2025-06-14T20:46:29.197Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a2/98/3f65e4853a4a45b0cf369e5bbb0d9efaad93589461d155119feb88e8ff7b/pyobjc_framework_callkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:bc1d2349dab93f7a0d298b01893828d7f46aded9122a341469b835d977a0646d", size = 11494, upload-time = "2025-06-14T20:46:30.09Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e4/95/d89e97351570fcfaae843dea29aa06c2a3ff00a6ea8ea4c3e68478620afa/pyobjc_framework_callkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:b69b4262897f2701348ea0da36afe32d60f84e2a036baf13e258a97875b25a6c", size = 11305, upload-time = "2025-06-14T20:46:31.099Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2f/38/939b73759cfd1bf6367290c31bfe576fafdd7a351aa867c7c29eba962d1e/pyobjc_framework_callkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8266ee797fdabb657f7cb4fa808404fc33fcf3f31d4bcab1ab3c53d272e1ff83", size = 11504, upload-time = "2025-06-14T20:46:31.784Z" },
]

[[package]]
name = "pyobjc-framework-carbon"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/39/a4/d751851865d9a78405cfec0c8b2931b1e96b9914e9788cd441fa4e8290d0/pyobjc_framework_carbon-11.1.tar.gz", hash = "sha256:047f098535479efa3ab89da1ebdf3cf9ec0b439a33a4f32806193886e9fcea71", size = 37291, upload-time = "2025-06-14T20:56:53.642Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/84/44/f1a20b5aa3833af4d461074c479263a410ef90d17dbec11f78ad9c34dbab/pyobjc_framework_carbon-11.1-py2.py3-none-any.whl", hash = "sha256:1bf66853e939315ad7ee968170b16dd12cb838c42b80dfcd5354687760998825", size = 4753, upload-time = "2025-06-14T20:46:33.141Z" },
]

[[package]]
name = "pyobjc-framework-cfnetwork"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/6f/49/7b24172e3d6eb0ddffc33a7498a2bea264aa2958c3fecaeb463bef88f0b8/pyobjc_framework_cfnetwork-11.1.tar.gz", hash = "sha256:ad600163eeadb7bf71abc51a9b6f2b5462a018d3f9bb1510c5ce3fdf2f22959d", size = 79069, upload-time = "2025-06-14T20:56:54.615Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c2/31/05b4fb79e7f738f7f7d7a58734de2fab47d9a1fb219c2180e8c07efe2550/pyobjc_framework_cfnetwork-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:70beb8095df76e0e8eb7ab218be1e69ae180e01a4d77f7cad73c97b4eb7a296a", size = 19141, upload-time = "2025-06-14T20:46:36.134Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2d/b1/5ea76ffd6413be8c65ec02e4552e3da3ee2bd37449e0854e3c8c559e7e42/pyobjc_framework_cfnetwork-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:5dd866fcbe6870931373636d19144544344f0f89685f6720e4a45453957702dd", size = 19148, upload-time = "2025-06-14T20:46:36.876Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ba/df/b4897033b0368e4b6c4e5f643c593801677b2590d48dcb93d1c5a1d66c0f/pyobjc_framework_cfnetwork-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:62ccc6dcaaa5877534d21f93a15861a3d8af95888123d659f9ff5383d1a2a1f4", size = 19406, upload-time = "2025-06-14T20:46:37.648Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/25/9b/f277fb7a7da804a2b53b2f3dacf1f0196e63536580023bd5377344e1407a/pyobjc_framework_cfnetwork-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4b998daa3e6ce253c48455365f004647b3b1da2f313fbc8a5a607e460b4d5567", size = 19186, upload-time = "2025-06-14T20:46:38.398Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e2/f6/80b5c7bb8247c2bb17c3869389a591f480ef771073c4642fbe49e65f1614/pyobjc_framework_cfnetwork-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2e9a4ce6b416bff881df499d9060c1096220ef8c20e519108a7b91692d1fd1d7", size = 19407, upload-time = "2025-06-14T20:46:39.143Z" },
]

[[package]]
name = "pyobjc-framework-cinematic"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/57/6f/c2d0b49e01e654496a1781bafb9da72a6fbd00f5abb39dc4a3a0045167c7/pyobjc_framework_cinematic-11.1.tar.gz", hash = "sha256:efde39a6a2379e1738dbc5434b2470cd187cf3114ffb81390b3b1abda470b382", size = 25522, upload-time = "2025-06-14T20:56:55.379Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/05/bd/a9b51c770bd96546a101c9e9994f851b87336f168a77048241517ca4db8c/pyobjc_framework_cinematic-11.1-py2.py3-none-any.whl", hash = "sha256:b62c024c1a9c7890481bc2fdfaf0cd3c251a4a08357d57dc1795d98920fcdbd1", size = 4562, upload-time = "2025-06-14T20:46:40.989Z" },
]

[[package]]
name = "pyobjc-framework-classkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/7a/8b/5150b4faddd15d5dd795bc62b2256c4f7dafc983cfa694fcf88121ea0016/pyobjc_framework_classkit-11.1.tar.gz", hash = "sha256:ee1e26395eb00b3ed5442e3234cdbfe925d2413185af38eca0477d7166651df4", size = 39831, upload-time = "2025-06-14T20:56:56.036Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/75/79/2552fd5e1da73dffb35589469b3cd8c0928e3100462761350d19ea922e59/pyobjc_framework_classkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:161dcb9b718649e6331a5eab5a76c2b43a9b322b15b37b3f8f9c5faad12ee6d1", size = 8911, upload-time = "2025-06-14T20:46:43.714Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/59/1c/a06623c3d78949c9d5eae7c7e753e6c8c75e2ae7a0b8ccae40a1b6180e0a/pyobjc_framework_classkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:08000deb43004d16fb39ccd83b3de30e1e3b72639a79d05206d7d5c15f005b3a", size = 8928, upload-time = "2025-06-14T20:46:44.426Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b3/c3/e0a966134c8022f1d922b27fea6a50ec1118c12fdfa65b2ce4efaa7c84d6/pyobjc_framework_classkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ef28d042964b0f757569e72df737bb049b531c33b7d06a705ce2dcfa4e6e45d8", size = 9082, upload-time = "2025-06-14T20:46:45.309Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c7/66/d5113269ee84bebc03576c53394e2b59c25da01f932f2e1cdfc5bd05a5a1/pyobjc_framework_classkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:be279d91f10d68ad9a256e96d26d8975e35b9b1bb304c82491766d29ad252b0d", size = 8958, upload-time = "2025-06-14T20:46:46.329Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ad/72/fff0a96bd7fd9a83ee074330070ebe4a53d99a3c0620c786bb59c04c4a7c/pyobjc_framework_classkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9a1b9d31f9b23e05b92769bbdb4ef2167a59b3b24aefa6af86448f5087a2e105", size = 9120, upload-time = "2025-06-14T20:46:47.015Z" },
]

[[package]]
name = "pyobjc-framework-cloudkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-accounts" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coredata" },
    { name = "pyobjc-framework-corelocation" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/58/a6/bfe5be55ed95704efca0e86b218155a9c801735107cedba3af8ea4580a05/pyobjc_framework_cloudkit-11.1.tar.gz", hash = "sha256:40d2dc4bf28c5be9b836b01e4d267a15d847d756c2a65530e1fcd79b2825e86d", size = 122778, upload-time = "2025-06-14T20:56:56.73Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/25/d9/5570a217cef8130708e860b86f4f22bb5827247c97121523a9dfd4784148/pyobjc_framework_cloudkit-11.1-py2.py3-none-any.whl", hash = "sha256:c583e40c710cf85ebe34173d1d2995e832a20127edc8899b2f35b13f98498af1", size = 10870, upload-time = "2025-06-14T20:46:48.781Z" },
]

[[package]]
name = "pyobjc-framework-cocoa"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/4b/c5/7a866d24bc026f79239b74d05e2cf3088b03263da66d53d1b4cf5207f5ae/pyobjc_framework_cocoa-11.1.tar.gz", hash = "sha256:87df76b9b73e7ca699a828ff112564b59251bb9bbe72e610e670a4dc9940d038", size = 5565335, upload-time = "2025-06-14T20:56:59.683Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/68/da/41c0f7edc92ead461cced7e67813e27fa17da3c5da428afdb4086c69d7ba/pyobjc_framework_cocoa-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:806de56f06dfba8f301a244cce289d54877c36b4b19818e3b53150eb7c2424d0", size = 388983, upload-time = "2025-06-14T20:46:52.591Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4e/0b/a01477cde2a040f97e226f3e15e5ffd1268fcb6d1d664885a95ba592eca9/pyobjc_framework_cocoa-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:54e93e1d9b0fc41c032582a6f0834befe1d418d73893968f3f450281b11603da", size = 389049, upload-time = "2025-06-14T20:46:53.757Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/bc/e6/64cf2661f6ab7c124d0486ec6d1d01a9bb2838a0d2a46006457d8c5e6845/pyobjc_framework_cocoa-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:fd5245ee1997d93e78b72703be1289d75d88ff6490af94462b564892e9266350", size = 393110, upload-time = "2025-06-14T20:46:54.894Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/33/87/01e35c5a3c5bbdc93d5925366421e10835fcd7b23347b6c267df1b16d0b3/pyobjc_framework_cocoa-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:aede53a1afc5433e1e7d66568cc52acceeb171b0a6005407a42e8e82580b4fc0", size = 392644, upload-time = "2025-06-14T20:46:56.503Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c1/7c/54afe9ffee547c41e1161691e72067a37ed27466ac71c089bfdcd07ca70d/pyobjc_framework_cocoa-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1b5de4e1757bb65689d6dc1f8d8717de9ec8587eb0c4831c134f13aba29f9b71", size = 396742, upload-time = "2025-06-14T20:46:57.64Z" },
]

[[package]]
name = "pyobjc-framework-collaboration"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/66/49/9dbe8407d5dd663747267c1234d1b914bab66e1878d22f57926261a3063b/pyobjc_framework_collaboration-11.1.tar.gz", hash = "sha256:4564e3931bfc51773623d4f57f2431b58a39b75cb964ae5c48d27ee4dde2f4ea", size = 16839, upload-time = "2025-06-14T20:57:01.101Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/62/24/4c9deedcc62d223a45d4b4fa16162729923d2b3e2231467de6ecd079f3f8/pyobjc_framework_collaboration-11.1-py2.py3-none-any.whl", hash = "sha256:3629ea5b56c513fb330d43952afabb2df2a2ac2f9048b8ec6e8ab4486191390a", size = 4891, upload-time = "2025-06-14T20:46:59.734Z" },
]

[[package]]
name = "pyobjc-framework-colorsync"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/b5/97/7613b6041f62c52f972e42dd5d79476b56b84d017a8b5e4add4d9cfaca36/pyobjc_framework_colorsync-11.1.tar.gz", hash = "sha256:7a346f71f34b2ccd1b020a34c219b85bf8b6f6e05283d503185aeb7767a269dd", size = 38999, upload-time = "2025-06-14T20:57:01.761Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/30/d5/c8fc7c47cbb9865058094dc9cf3f57879156ff55fb261cf199e7081d1db7/pyobjc_framework_colorsync-11.1-py2.py3-none-any.whl", hash = "sha256:d19d6da2c7175a3896a63c9b40a8ab98ade0779a5b40062789681501c33efd5c", size = 5971, upload-time = "2025-06-14T20:47:00.547Z" },
]

[[package]]
name = "pyobjc-framework-contacts"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a6/85/34868b6447d552adf8674bac226b55c2baacacee0d67ee031e33805d6faa/pyobjc_framework_contacts-11.1.tar.gz", hash = "sha256:752036e7d8952a4122296d7772f274170a5f35a53ee6454a27f3e1d9603222cc", size = 84814, upload-time = "2025-06-14T20:57:02.582Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/30/c8/0d47af11112bf382e059cfe2dd03be98914f0621ddff8858bb9af864f8c5/pyobjc_framework_contacts-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:576ee4aec05d755444bff10b45833f73083b5b3d1b2740e133b92111f7765e54", size = 12141, upload-time = "2025-06-14T20:47:02.884Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/11/af/375aa44e9e00aa66e373c4c3893a0db341d93f90e2d62a277287dc553841/pyobjc_framework_contacts-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:09b873d2bd739fea63d744430defb04ce4b44af064aaf0b6bf558eea23f82bd7", size = 12160, upload-time = "2025-06-14T20:47:03.614Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a0/b9/effeda0eefedced16d4a002ab0c0a331be506d5bc7ff290788ac8eb0b2a9/pyobjc_framework_contacts-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:23312bb4bfc5aafecdac84ca402189e312e754e9dc0586d8f282d225c3952c00", size = 12319, upload-time = "2025-06-14T20:47:04.316Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/93/9c/25c6e7ba0fe1d18206decd3e2b47bf110047dda89f7411fe430c0bfd4268/pyobjc_framework_contacts-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3409aba6e23cb179b3fe932c1a0a53d7b273ac8292d5adf1bf6849e925cc0955", size = 12237, upload-time = "2025-06-14T20:47:05.01Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/32/fc/0a519a38eada4bf4ed6f502920077e5313fdb1f3eec668438460a797ce47/pyobjc_framework_contacts-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:910f40a2e4d80a97f282bfdecba0f5ff95201b11844acd3f9cb9522db364ab57", size = 12393, upload-time = "2025-06-14T20:47:05.707Z" },
]

[[package]]
name = "pyobjc-framework-contactsui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-contacts" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/3f/57/8765b54a30edaa2a56df62e11e7c32e41b6ea300513256adffa191689368/pyobjc_framework_contactsui-11.1.tar.gz", hash = "sha256:5bc29ea2b10a342018e1b96be6b140c10ebe3cfb6417278770feef5e88026a1f", size = 20031, upload-time = "2025-06-14T20:57:03.603Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/46/b6/50ec09f1bb18c422b8c079e02328689f32e977b43ab7651c05e8274854dc/pyobjc_framework_contactsui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c34a6f27ef5aa4742cc44fd5b4d16fe1e1745ff839578b4c059faf2c58eee3ca", size = 7875, upload-time = "2025-06-14T20:47:09.041Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8b/3f/72170303c11945c360b83fa1c0d3f91638dc5de1ef9f9a2b880252378430/pyobjc_framework_contactsui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f3b4f0225645a26ed9e6c008c2e8c217035b4a50fa9cd6623c628a11c37924d0", size = 7886, upload-time = "2025-06-14T20:47:09.726Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ad/d7/fd11ac75bd6eb5d23225f7d1ac910c2b47481caff6e04b883bec04c28de2/pyobjc_framework_contactsui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:666586174b306b33b791d2edee021cd979a8c970d444f906ed294e27583a6b54", size = 8044, upload-time = "2025-06-14T20:47:10.427Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/05/64/aee816b82564c693fea199178ac791dd384d602b6c772b7f829fb1b8405d/pyobjc_framework_contactsui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:7901eed3c669ad52cca86089c443fd30820b21586bf758e03fb83696f435ba87", size = 7937, upload-time = "2025-06-14T20:47:11.182Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/34/d4/fe2495ac19d83cc211a639b3654d4ea0f173d053cca387a4448a70d1a1f6/pyobjc_framework_contactsui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8b03bd175095b4774c55bd5f38a01942e945b668bea15b9dc3b4f1a28b1a8696", size = 8091, upload-time = "2025-06-14T20:47:11.884Z" },
]

[[package]]
name = "pyobjc-framework-coreaudio"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/39/c0/4ab6005cf97e534725b0c14b110d4864b367c282b1c5b0d8f42aad74a83f/pyobjc_framework_coreaudio-11.1.tar.gz", hash = "sha256:b7b89540ae7efc6c1e3208ac838ef2acfc4d2c506dd629d91f6b3b3120e55c1b", size = 141032, upload-time = "2025-06-14T20:57:04.348Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3d/fe/c43521642db98a4ec29fa535781c1316342bb52d5fc709696cbb1e8ca6cd/pyobjc_framework_coreaudio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2538d1242dab4e27efb346eafbad50594e7e95597fa7220f0bab2099c825da55", size = 36765, upload-time = "2025-06-14T20:47:15.344Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/82/9b/24d03ace273585de2d04385f06b895ce92caf8f5af430b060618ebce9dbe/pyobjc_framework_coreaudio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f73d996df1e721931d9f78050e1708735a173dbe3a76d9c71fb36e04f7208478", size = 36779, upload-time = "2025-06-14T20:47:16.123Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/91/23/aa78365e45d0d04fc37e21cf7d69dc0d11e17b564e83cb5bcd98e89cdf45/pyobjc_framework_coreaudio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:67dae111b78d91c26c753dbfbccc3ea5498cfda3dfe83c6f3778628b435e1e7b", size = 38480, upload-time = "2025-06-14T20:47:16.911Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3e/58/fc6d752a68f28567fa6d6d6a229122c829e2251f79ec7304fe0572e0fdcd/pyobjc_framework_coreaudio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9527a16a2b88b37bace578d499f21229f9a33b9afdcdd35d4f44374cb8eb9ab6", size = 36910, upload-time = "2025-06-14T20:47:17.69Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9e/4c/c1c5624418dea005d9965ba690d3649afc33371ade213841ab51922af751/pyobjc_framework_coreaudio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6ba8b67f185c0e3f26b17ae525cee3f411bc8d6e9c9a8bfd899a28f594623d2f", size = 38567, upload-time = "2025-06-14T20:47:18.45Z" },
]

[[package]]
name = "pyobjc-framework-coreaudiokit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/f1/4e/c49b26c60047c511727efe994b412276c487dfe90f1ee0fced0bddbdf8a3/pyobjc_framework_coreaudiokit-11.1.tar.gz", hash = "sha256:0b461c3d6123fda4da6b6aaa022efc918c1de2e126a5cf07d2189d63fa54ba40", size = 21955, upload-time = "2025-06-14T20:57:05.218Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/27/d8ff6293851a7d9665724fa5c324d28200776ec10a04b850ba21ad1f9be1/pyobjc_framework_coreaudiokit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:20440a2926b1d91da8efc8bc060e77c7a195cb0443dbf3770eaca9e597276748", size = 7266, upload-time = "2025-06-14T20:47:22.136Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/13/e6/89aa525271d19f0ea11799021f364181dd62dbfe77ecb4fc0a7d4e579cd2/pyobjc_framework_coreaudiokit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:11d42770dfbc6a8af8d5fa39a4f700f0067d7e6c7ba9335e6624d89de3c599a9", size = 7273, upload-time = "2025-06-14T20:47:23.137Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a5/70/f9b13b7822a53bed794525214ccca63b018901c113ebfd45e2159447f3cf/pyobjc_framework_coreaudiokit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6fea7c7ea5305e8cbd75808ec4edcde8e2320137f227b3d771266dd9a71e1fa5", size = 7429, upload-time = "2025-06-14T20:47:24.17Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a7/d0/aba10b553783c9940b81cb67ad3cae4d4c72e67d4c1af8f4cbe2d9a642d8/pyobjc_framework_coreaudiokit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a71447196a48869b551a2e3b6ba92f39241cb64d0257120505c62ddb611aef0f", size = 7301, upload-time = "2025-06-14T20:47:25.023Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/90/9a/a4b7fc47896f1739b8346d21c1b40f536e317f3de416b5cbf12c50445979/pyobjc_framework_coreaudiokit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8d012561eb95877f0214aa0cd13043b1a2693add4a9534d1e6fb82f6d7183c7c", size = 7451, upload-time = "2025-06-14T20:47:26.063Z" },
]

[[package]]
name = "pyobjc-framework-corebluetooth"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/3d/fe/2081dfd9413b7b4d719935c33762fbed9cce9dc06430f322d1e2c9dbcd91/pyobjc_framework_corebluetooth-11.1.tar.gz", hash = "sha256:1deba46e3fcaf5e1c314f4bbafb77d9fe49ec248c493ad00d8aff2df212d6190", size = 60337, upload-time = "2025-06-14T20:57:05.919Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8a/bc/083ea1ae57a31645df7fad59921528f6690995f7b7c84a203399ded7e7fe/pyobjc_framework_corebluetooth-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:36bef95a822c68b72f505cf909913affd61a15b56eeaeafea7302d35a82f4f05", size = 13163, upload-time = "2025-06-14T20:47:29.624Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3e/b5/d07cfa229e3fa0cd1cdaa385774c41907941d25b693cf55ad92e8584a3b3/pyobjc_framework_corebluetooth-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:992404b03033ecf637e9174caed70cb22fd1be2a98c16faa699217678e62a5c7", size = 13179, upload-time = "2025-06-14T20:47:30.376Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7a/10/476bca43002a6d009aed956d5ed3f3867c8d1dcd085dde8989be7020c495/pyobjc_framework_corebluetooth-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ebb8648f5e33d98446eb1d6c4654ba4fcc15d62bfcb47fa3bbd5596f6ecdb37c", size = 13358, upload-time = "2025-06-14T20:47:31.114Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b0/49/6c050dffb9acc49129da54718c545bc5062f61a389ebaa4727bc3ef0b5a9/pyobjc_framework_corebluetooth-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e84cbf52006a93d937b90421ada0bc4a146d6d348eb40ae10d5bd2256cc92206", size = 13245, upload-time = "2025-06-14T20:47:31.939Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/36/15/9068e8cb108e19e8e86cbf50026bb4c509d85a5d55e2d4c36e292be94337/pyobjc_framework_corebluetooth-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4da1106265d7efd3f726bacdf13ba9528cc380fb534b5af38b22a397e6908291", size = 13439, upload-time = "2025-06-14T20:47:32.66Z" },
]

[[package]]
name = "pyobjc-framework-coredata"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/00/e3/af497da7a7c895b6ff529d709d855a783f34afcc4b87ab57a1a2afb3f876/pyobjc_framework_coredata-11.1.tar.gz", hash = "sha256:fe9fd985f8e06c70c0fb1e6bbea5b731461f9e76f8f8d8e89c7c72667cdc6adf", size = 260628, upload-time = "2025-06-14T20:57:06.729Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5b/ac/77935aa9891bd6be952b1e6780df2bae748971dd0fe0b5155894004840bd/pyobjc_framework_coredata-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c9b2374784e67694a18fc8c120a12f11b355a20b643c01f23ae2ce87330a75e0", size = 16443, upload-time = "2025-06-14T20:47:35.711Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/75/50/17631c3f172d9681faad210b035fa3d2c01f59468b574dbc088512853cc2/pyobjc_framework_coredata-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:007160eb10bb8c789076f231e3d625d8875ca42eb5a806fdab5d0277c48866f8", size = 16457, upload-time = "2025-06-14T20:47:36.439Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1f/d7/c736d0a945efe806996335324a241f9e2726ebc8a91c9c3cfaa2d788c63b/pyobjc_framework_coredata-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:699ad568f98f58e88e642159c91ffff0c68ce3d1ec798e4af8333b27431fd058", size = 16608, upload-time = "2025-06-14T20:47:37.526Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fa/b9/22c554e3a7d121145aedaab580a88bf35935fc81f693e5071ed8aa7d299e/pyobjc_framework_coredata-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d84afaccbb4f18dbda4c557cd059b7adc2116436a065353e25e7cbc840d9f8b4", size = 16500, upload-time = "2025-06-14T20:47:38.271Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d1/2e/8562252a30644ac5209365358a30cfc53a46609959beaafceffde7381e54/pyobjc_framework_coredata-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:89dde863eff01ed6b5f8d88c764a08b154ef37078397c98c5f403e8798723b9d", size = 16659, upload-time = "2025-06-14T20:47:39.042Z" },
]

[[package]]
name = "pyobjc-framework-corehaptics"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/5f/83/cc997ec4687a68214dd3ad1bdf64353305f5c7e827fad211adac4c28b39f/pyobjc_framework_corehaptics-11.1.tar.gz", hash = "sha256:e5da3a97ed6aca9b7268c8c5196c0a339773a50baa72d1502d3435dc1a2a80f1", size = 42722, upload-time = "2025-06-14T20:57:08.019Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/21/d0/0fb20c0f19beae53c905653ffdcbf32e3b4119420c737ff4733f7ebb3b29/pyobjc_framework_corehaptics-11.1-py2.py3-none-any.whl", hash = "sha256:8f8c47ccca5052d07f95d2f35e6e399c5ac1f2072ba9d9eaae902edf4e3a7af4", size = 5363, upload-time = "2025-06-14T20:47:40.582Z" },
]

[[package]]
name = "pyobjc-framework-corelocation"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/95/ef/fbd2e01ec137208af7bfefe222773748d27f16f845b0efa950d65e2bd719/pyobjc_framework_corelocation-11.1.tar.gz", hash = "sha256:46a67b99925ee3d53914331759c6ee110b31bb790b74b05915acfca41074c206", size = 104508, upload-time = "2025-06-14T20:57:08.731Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/95/cb/282d59421cdb89a5e5fcce72fc37d6eeace98a2a86d71f3be3cd47801298/pyobjc_framework_corelocation-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:562e31124f80207becfd8df01868f73fa5aa70169cc4460e1209fb16916e4fb4", size = 12752, upload-time = "2025-06-14T20:47:43.273Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/de/cb/c4672fcfa5e998cfd0dd165717ec312f7e6cbac06ecb4a0e227dbc4d7e27/pyobjc_framework_corelocation-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:0f8182835429118a55ed65963c80f5b2892d190747b986e8395b1cd99f41a1d0", size = 12768, upload-time = "2025-06-14T20:47:43.987Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/47/e7/ef83b4d6fca57bd09a56064fdcb55792b7497279b1dac3de781c86ed40ec/pyobjc_framework_corelocation-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:bc3f27802415aa62330a2d2507adc3a9b98a89d6de7d1033ebe6b8c461610831", size = 12910, upload-time = "2025-06-14T20:47:44.744Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a3/9f/9a107d223babd3d846873bd30897d4411585523403adfaec91963abcb281/pyobjc_framework_corelocation-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:17ce2530bd5a0dca9059eb11bc647d920490bcdd35b5cac1e160f51f0297bdc8", size = 12800, upload-time = "2025-06-14T20:47:45.477Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0d/54/3a841006c2bf0fa4797c2fb77c79150b526800d191a539a8f2d0e54a377e/pyobjc_framework_corelocation-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a384d9fcba2c041d8f8115b51a07ef11c391bc30f72560aaea8b94db6b3b225c", size = 12953, upload-time = "2025-06-14T20:47:46.499Z" },
]

[[package]]
name = "pyobjc-framework-coremedia"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/95/5d/81513acd219df77a89176f1574d936b81ad6f6002225cabb64d55efb7e8d/pyobjc_framework_coremedia-11.1.tar.gz", hash = "sha256:82cdc087f61e21b761e677ea618a575d4c0dbe00e98230bf9cea540cff931db3", size = 216389, upload-time = "2025-06-14T20:57:09.546Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2c/d1/b3d004d6a2d2188d196779d92fe8cfa2533f5722cd216fbc4f0cffc63b24/pyobjc_framework_coremedia-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ea5055298af91e463ffa7977d573530f9bada57b8f2968dcc76a75e339b9a598", size = 29015, upload-time = "2025-06-14T20:47:49.655Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1c/23/cafd29011d14eac27fc55770157ebb8e02ffed9f75e01f24e97616417c4c/pyobjc_framework_coremedia-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7ecdb64c743ffe9fd3949c7cc9109891b9f399a0852717fcb969d33c4e7ba527", size = 29031, upload-time = "2025-06-14T20:47:50.395Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/de/a6/ca85b7d9d000e8e2748bcacde356278cb90f6ca9aed54dce6a42d1716ba8/pyobjc_framework_coremedia-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:969ce357c616f6835f47e27d1e73964374cdb671476571dfd358894a8ced06f2", size = 29094, upload-time = "2025-06-14T20:47:51.318Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b8/3d/56d530cf504a6eef84f51c8f6f845af8b947f6108e41db5e0b5189d5a667/pyobjc_framework_coremedia-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bf1da05c297776c297ab3489ebf18d954efdff530acbdd6e70c32be811e20ec6", size = 29043, upload-time = "2025-06-14T20:47:52.092Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a4/bc/b237ecd4954a0f07450469236ca45412edb7d8715ff7fc175ac519e7c472/pyobjc_framework_coremedia-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:aa942d9ad0cf5bc4d3ede8779c3fac2f04cf3857687f2fb8505bae3378d04b95", size = 29111, upload-time = "2025-06-14T20:47:53.083Z" },
]

[[package]]
name = "pyobjc-framework-coremediaio"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/64/68/9cef2aefba8e69916049ff43120e8794df8051bdf1f690a55994bbe4eb57/pyobjc_framework_coremediaio-11.1.tar.gz", hash = "sha256:bccd69712578b177144ded398f4695d71a765ef61204da51a21f0c90b4ad4c64", size = 108326, upload-time = "2025-06-14T20:57:10.435Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4b/b5/5dd941c1d7020a78b563a213fb8be7c6c3c1073c488914e158cd5417f4f7/pyobjc_framework_coremediaio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:39ad2518de9943c474e5ca0037e78f92423c3352deeee6c513a489016dac1266", size = 17250, upload-time = "2025-06-14T20:47:56.505Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/08/44/cd98e1dacdd28c4e80fe1b0dde3a5171494735cb4a7b8b5775825b824b96/pyobjc_framework_coremediaio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9e0a079fe790ce8a69d11bea46b315c9a0d3f3999a2f09e2ef4fcc4430a47c42", size = 17226, upload-time = "2025-06-14T20:47:57.267Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f9/66/89a3c01d1d1a0e7b510ade14a2c604883d6846d8279095ff4849f9989f9c/pyobjc_framework_coremediaio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:5a94f9e507b470ce7dcb887e79ccf19e98693a606ad34462d711004e3edd88c3", size = 17564, upload-time = "2025-06-14T20:47:58.483Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2b/70/4a137a8a8b618ad025586ebe7f459989ead666e41825053d297c1a104f72/pyobjc_framework_coremediaio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0a7ffded00a7dc6f0bf4a44a6832f0150d45a83886486148b71ccc67c70ef215", size = 17257, upload-time = "2025-06-14T20:47:59.244Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1b/d7/054313e96c40efe8f535ef1a172cc612c53a55f27eb5e2805a84727155d6/pyobjc_framework_coremediaio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5ff161025ef28d5e2eed90db0e8b828cb361281b799b16b1885711ca0addc1aa", size = 17572, upload-time = "2025-06-14T20:48:00.01Z" },
]

[[package]]
name = "pyobjc-framework-coremidi"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/06/ca/2ae5149966ccd78290444f88fa62022e2b96ed2fddd47e71d9fd249a9f82/pyobjc_framework_coremidi-11.1.tar.gz", hash = "sha256:095030c59d50c23aa53608777102bc88744ff8b10dfb57afe24b428dcd12e376", size = 107817, upload-time = "2025-06-14T20:57:11.245Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c2/2d/57c279dd74a9073d1416b10b05ebb9598f4868cad010d87f46ef4b517324/pyobjc_framework_coremidi-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:deb9120478a831a898f22f68737fc683bb9b937e77556e78b75986aebd349c55", size = 24277, upload-time = "2025-06-14T20:48:03.184Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1e/66/dfdc7a5dc5a44b1660015bb24454ca0cbdf436e631e39917c495475dbb24/pyobjc_framework_coremidi-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c2e1ab122501206ceae07123fdc433e91a5f1a97224f80ece0717b6f36ad2029", size = 24308, upload-time = "2025-06-14T20:48:04.285Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/46/fe/200f286d5506efdc6c6d150eda24909a89f5c856a7a5003db0a423f66943/pyobjc_framework_coremidi-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3462a158214adb7ebc785fb6924e674c58dcd471888dbca5e2e77381f3f1bbdc", size = 24463, upload-time = "2025-06-14T20:48:05.014Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7e/a5/053ad95a662544ef036c18d45680a4016b9eb897fb7dfcbcef13602b947a/pyobjc_framework_coremidi-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f4b70864cae295f27b5d51817c0768fade7c1335a59410910146e5f2a54c475c", size = 24320, upload-time = "2025-06-14T20:48:06.104Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7d/2c/e97e4f8ea07ffca82daa0ed0159f6d5ca03699b2a1944f4c4adb4d64bd21/pyobjc_framework_coremidi-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2ef1a10f6230fce82b931670470158404657d9fb9ac558a77b46b547e9978524", size = 24474, upload-time = "2025-06-14T20:48:06.847Z" },
]

[[package]]
name = "pyobjc-framework-coreml"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/0d/5d/4309f220981d769b1a2f0dcb2c5c104490d31389a8ebea67e5595ce1cb74/pyobjc_framework_coreml-11.1.tar.gz", hash = "sha256:775923eefb9eac2e389c0821b10564372de8057cea89f1ea1cdaf04996c970a7", size = 82005, upload-time = "2025-06-14T20:57:12.004Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3e/9e/a1b6d30b4f91c7cc4780e745e1e73a322bd3524a773f66f5eac0b1600d85/pyobjc_framework_coreml-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c768b03d72488b964d753392e9c587684961d8237b69cca848b3a5a00aea79c9", size = 11436, upload-time = "2025-06-14T20:48:10.048Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/95/95/f8739958ccf7cbaaf172653b3665cfcee406c5503a49828130b618b93d3f/pyobjc_framework_coreml-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:10d51f8a5fe8d30c7ec70304a2324df76b48b9fbef30ee0f0c33b99a49ae8853", size = 11452, upload-time = "2025-06-14T20:48:10.74Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/57/d1/881cef8f09f022ba6534d98f0bb1c3ad5e68dbdda91173d88fa1524c0526/pyobjc_framework_coreml-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4df25ee233430f016ffcb4e88506b54c8e7b668c93197e6a1341761530a5922c", size = 11682, upload-time = "2025-06-14T20:48:11.421Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/cf/92/81be40d2b4a9a52e75ff0051dfd9258cf5aad529d86144f0730d1f7ec034/pyobjc_framework_coreml-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:287a2a059016d02d8c40e0d29e70226142a4969db97ad79cefc70ec9bf0ab29e", size = 11551, upload-time = "2025-06-14T20:48:12.425Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b7/08/bb686f0ede51d1e09be395f176613ee4834f47ce081c13e4ee464d14c748/pyobjc_framework_coreml-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a479c3d759aff3695f72c7915a78df6e92e0eca7027abaa8b4a07e876ba1dbfb", size = 11729, upload-time = "2025-06-14T20:48:13.135Z" },
]

[[package]]
name = "pyobjc-framework-coremotion"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a5/95/e469dc7100ea6b9c29a074a4f713d78b32a78d7ec5498c25c83a56744fc2/pyobjc_framework_coremotion-11.1.tar.gz", hash = "sha256:5884a568521c0836fac39d46683a4dea3d259a23837920897042ffb922d9ac3e", size = 67050, upload-time = "2025-06-14T20:57:12.705Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e9/17/ffa3cf9fde9df31f3d6ecb38507c61c6d8d81276d0a9116979cafd5a0ab7/pyobjc_framework_coremotion-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8c3b33228a170bf8495508a8923451ec600435c7bff93d7614f19c913baeafd1", size = 10368, upload-time = "2025-06-14T20:48:16.066Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7c/2b/ade312f6bda6c368112bc2151834e664c22ae7d6d1f2ce33347b84ece7fb/pyobjc_framework_coremotion-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ac5302deaab99a7443cad63f125061a90040852d4f8efb58492542a612b2afe3", size = 10393, upload-time = "2025-06-14T20:48:16.784Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/63/51/380d1b2b072b379a4740b725bdec4119c0c82bc66c55a2a62ca2fa0ec478/pyobjc_framework_coremotion-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d67413a56989154dab7bf1b69c14b0b2387d87d3a4c8e3c8a9fc0230f061e8ab", size = 10534, upload-time = "2025-06-14T20:48:17.466Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/03/4f/efbab9157e74d39074a3ce05e0494174203cbdb28a48c59fb2464b0fffed/pyobjc_framework_coremotion-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:42fb307b86999d078503ff79bdf8df4d1c27d38763db6b1c5c0f4054241f67a3", size = 10443, upload-time = "2025-06-14T20:48:18.532Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/90/1da8d8acbcd8fe348bd2e94a26e5f289e621af1d42f86c57b4d3de940650/pyobjc_framework_coremotion-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:708431c53f483bc6da199375227ffea1b4e8e7d8c81d162492db3fc36893fb53", size = 10606, upload-time = "2025-06-14T20:48:19.228Z" },
]

[[package]]
name = "pyobjc-framework-coreservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-fsevents" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a8/a9/141d18019a25776f507992f9e7ffc051ca5a734848d8ea8d848f7c938efc/pyobjc_framework_coreservices-11.1.tar.gz", hash = "sha256:cf8eb5e272c60a96d025313eca26ff2487dcd02c47034cc9db39f6852d077873", size = 1245086, upload-time = "2025-06-14T20:57:13.914Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fa/0f/52827197a1fa1dabefd77803920eaf340f25e0c81944844ab329d511cade/pyobjc_framework_coreservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6bd313ec326efd715b4b10c3ebcc9f054e3ee3178be407b97ea225cd871351d2", size = 30252, upload-time = "2025-06-14T20:48:22.657Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9d/dc/8a0414dd81054062a56a54db5c1cbb35c715081c9210ed69d5fed8046ebe/pyobjc_framework_coreservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8aee505dca56afc5363d8d0dff0b2d26583a8d0f3ac37674cef86f66c51a2934", size = 30271, upload-time = "2025-06-14T20:48:23.427Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/44/e3/494bbc589b0a02ad7ab657fdf67359298b007112b65a2f4416d61176a4c4/pyobjc_framework_coreservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4ffa188322ab9d05c6964926959dedba5cc04534232f1eff03aee5f09faa499e", size = 30282, upload-time = "2025-06-14T20:48:24.175Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ab/0b/1c666c01c003e1b73baa5c71cab5a50000b1180e5c1cbf14b02f20cf8c3b/pyobjc_framework_coreservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:21e9e86192d719cd5c899cc0e931110733da0b5bbbf606681e5fccd4dd39c174", size = 30294, upload-time = "2025-06-14T20:48:24.923Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ff/39/6026aaeef8b0eb0c25089374132a9bdbeffbc10f93cab589162efd43dc86/pyobjc_framework_coreservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:74dcc295245f07754328bada9577b189e3abef71607d013e939751c1b5b55729", size = 30309, upload-time = "2025-06-14T20:48:25.706Z" },
]

[[package]]
name = "pyobjc-framework-corespotlight"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/31/c7/b67ebfb63b7ccbfda780d583056d1fd4b610ba3839c8ebe3435b86122c61/pyobjc_framework_corespotlight-11.1.tar.gz", hash = "sha256:4dd363c8d3ff7619659b63dd31400f135b03e32435b5d151459ecdacea14e0f2", size = 87161, upload-time = "2025-06-14T20:57:14.934Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/f8/06b7edfeabe5b3874485b6e5bbe4a39d9f2e1f44348faa7cb320fbc6f21a/pyobjc_framework_corespotlight-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7cedd3792fe1fe2a8dc65a8ff1f70baf12415a5dc9dc4d88f987059567d7e694", size = 9977, upload-time = "2025-06-14T20:48:28.757Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7d/ce/812ae5a7f97a57abce1b2232280d5838a77d5454e5b05d79c3e654ad7400/pyobjc_framework_corespotlight-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:546d0d9b101de4ca20449f3807d1f88e5c26de0345a8bfefc70f12f87efb8433", size = 9997, upload-time = "2025-06-14T20:48:29.833Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5c/ee/9c432c1735f537c5b56dae43f6d2f2dd4922cac45c8e072e5a405b3ab81b/pyobjc_framework_corespotlight-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f562cc65865066f8e2e5d96c868fd7f463d8280f1ef01df85250fc1150feed0e", size = 10137, upload-time = "2025-06-14T20:48:30.513Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c1/b8/3a8910e0ffbec9f13f090be0e7cd40ad8144069dcdb80062f13c4768be5c/pyobjc_framework_corespotlight-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bce3d84f97014228b244c734aea3ec03b257573b22c097dff4eb176a80cd29a9", size = 10043, upload-time = "2025-06-14T20:48:31.218Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b5/7e/36e3342da3f5d05979729570c1630e442305118d5cb6462e81d21feb74e7/pyobjc_framework_corespotlight-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f59d0d2f0411db102d16490e47b457b994c613f1b980869fa3a151863da7aa4c", size = 10188, upload-time = "2025-06-14T20:48:31.906Z" },
]

[[package]]
name = "pyobjc-framework-coretext"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/65/e9/d3231c4f87d07b8525401fd6ad3c56607c9e512c5490f0a7a6abb13acab6/pyobjc_framework_coretext-11.1.tar.gz", hash = "sha256:a29bbd5d85c77f46a8ee81d381b847244c88a3a5a96ac22f509027ceceaffaf6", size = 274702, upload-time = "2025-06-14T20:57:16.059Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/32/67/9cc5189c366e67dc3e5b5976fac73cc6405841095f795d3fa0d5fc43d76a/pyobjc_framework_coretext-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1597bf7234270ee1b9963bf112e9061050d5fb8e1384b3f50c11bde2fe2b1570", size = 30175, upload-time = "2025-06-14T20:48:35.023Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b0/d1/6ec2ef4f8133177203a742d5db4db90bbb3ae100aec8d17f667208da84c9/pyobjc_framework_coretext-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:37e051e8f12a0f47a81b8efc8c902156eb5bc3d8123c43e5bd4cebd24c222228", size = 30180, upload-time = "2025-06-14T20:48:35.766Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0a/84/d4a95e49f6af59503ba257fbed0471b6932f0afe8b3725c018dd3ba40150/pyobjc_framework_coretext-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:56a3a02202e0d50be3c43e781c00f9f1859ab9b73a8342ff56260b908e911e37", size = 30768, upload-time = "2025-06-14T20:48:36.869Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/64/4c/16e1504e06a5cb23eec6276835ddddb087637beba66cf84b5c587eba99be/pyobjc_framework_coretext-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:15650ba99692d00953e91e53118c11636056a22c90d472020f7ba31500577bf5", size = 30155, upload-time = "2025-06-14T20:48:37.948Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ad/a4/cbfa9c874b2770fb1ba5c38c42b0e12a8b5aa177a5a86d0ad49b935aa626/pyobjc_framework_coretext-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:fb27f66a56660c31bb956191d64b85b95bac99cfb833f6e99622ca0ac4b3ba12", size = 30768, upload-time = "2025-06-14T20:48:38.734Z" },
]

[[package]]
name = "pyobjc-framework-corewlan"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/c6/d8/03aff3c75485fc999e260946ef1e9adf17640a6e08d7bf603d31cfcf73fc/pyobjc_framework_corewlan-11.1.tar.gz", hash = "sha256:4a8afea75393cc0a6fe696e136233aa0ed54266f35a47b55a3583f4cb078e6ce", size = 65792, upload-time = "2025-06-14T20:57:16.931Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/09/8a/74feabaad1225eb2c44d043924ed8caea31683e6760cd9b918b8d965efea/pyobjc_framework_corewlan-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7bd0775d2466ad500aad4747d8a889993db3a14240239f30ef53c087745e9c8e", size = 10016, upload-time = "2025-06-14T20:48:41.792Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ef/12/792146e163aa4504bc7870c77c4ec2425f9a05fa615a2b5c9cbec89b0fc6/pyobjc_framework_corewlan-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3c66643a97fcf3aa797fda997a3afc28d8d9bba9727dd5c0e68a313899d780f7", size = 10026, upload-time = "2025-06-14T20:48:42.626Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d8/e8/e0bf4c66192e85fb92a3ae01b50e34f2283568f7a0e5548f52db81b8b146/pyobjc_framework_corewlan-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6dc28264b56b18096c8869cce3f85e519fd27936f19524bb77458572ccfd7518", size = 10178, upload-time = "2025-06-14T20:48:43.309Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8e/c1/c860300f585de3f57b9f6c30c554e10708d57ec5ac1e920214b496638c0c/pyobjc_framework_corewlan-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:872de75409a710bb9a461e64e97185f8489d01898ec1b02c3e058c04606b61cf", size = 10051, upload-time = "2025-06-14T20:48:43.993Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ff/76/5bdb6b672d7b59a477cfcb35d7c0166a4bd86e7bc571ff693d62fccb75b2/pyobjc_framework_corewlan-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:14c7af9135ba0a920192af4dc50219bbf6185fcbb5de7041f097e1a1c8509587", size = 10210, upload-time = "2025-06-14T20:48:44.717Z" },
]

[[package]]
name = "pyobjc-framework-cryptotokenkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/eb/92/7fab6fcc6bb659d6946cfb2f670058180bcc4ca1626878b0f7c95107abf0/pyobjc_framework_cryptotokenkit-11.1.tar.gz", hash = "sha256:5f82f44d9ab466c715a7c8ad4d5ec47c68aacd78bd67b5466a7b8215a2265328", size = 59223, upload-time = "2025-06-14T20:57:17.658Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/76/f1/4cb9c90a55ec13301d60ac1c4d774c37b4ebc6db6331d3853021c933fcc8/pyobjc_framework_cryptotokenkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6384cb1d86fc586e2da934a5a37900825bd789e3a5df97517691de9af354af0c", size = 12543, upload-time = "2025-06-14T20:48:48.079Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c6/c8/b64a56ed65719b1dfb9c06da0772d4a76eceb830672aab237df745bc31f7/pyobjc_framework_cryptotokenkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a55c0e57ab164aa5ce562e4d9e69026339067ecb4888638995690f1c43b79cfa", size = 12559, upload-time = "2025-06-14T20:48:49.115Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9a/32/bb53ae388a99927fee626ba2746d3a6ec388cbc14b8f4ce91a35dd6b55e2/pyobjc_framework_cryptotokenkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:cb3e1bd344e794cb98343171b5501a1a3b75548ef5385bda3d5ec613c0b98045", size = 12742, upload-time = "2025-06-14T20:48:49.837Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4a/34/9f30580ccddff6b6555603af920ef61a420ba515eb8ab7e10fbd9c1464a5/pyobjc_framework_cryptotokenkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:faab9493e36095c0257598e25ef81c50bcdb3afb5843a82e6dfad8c7d1f47bcf", size = 12531, upload-time = "2025-06-14T20:48:51.634Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4e/07/baec88c0cfe9cd327753ce527dfab3b622bb5e2b45d3ff5bb8f4d2dae40c/pyobjc_framework_cryptotokenkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:efd89e5b024475701f6e9bec4cf1c2563e1bab37e79288397e09d9ad4e53d174", size = 12734, upload-time = "2025-06-14T20:48:52.396Z" },
]

[[package]]
name = "pyobjc-framework-datadetection"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/7d/4d/65c61d8878b44689e28d5729be9edbb73e20b1b0500d1095172cfd24aea6/pyobjc_framework_datadetection-11.1.tar.gz", hash = "sha256:cbe0080b51e09b2f91eaf2a9babec3dcf2883d7966bc0abd8393ef7abfcfc5db", size = 13485, upload-time = "2025-06-14T20:57:18.829Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/08/c4/ef2136e4e0cc69b02479295822aa33c8e26995b265c8a1184867b65a0a06/pyobjc_framework_datadetection-11.1-py2.py3-none-any.whl", hash = "sha256:5afd3dde7bba3324befb7a3133c9aeaa5088efd72dccc0804267a74799f4a12f", size = 3482, upload-time = "2025-06-14T20:48:54.301Z" },
]

[[package]]
name = "pyobjc-framework-devicecheck"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/f3/f2/b1d263f8231f815a9eeff15809f4b7428dacdc0a6aa267db5ed907445066/pyobjc_framework_devicecheck-11.1.tar.gz", hash = "sha256:8b05973eb2673571144d81346336e749a21cec90bd7fcaade76ffd3b147a0741", size = 13954, upload-time = "2025-06-14T20:57:19.782Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/39/72/17698a0d68b1067b20b32b4afd74bcafb53a7c73ae8fc608addc7b9e7a37/pyobjc_framework_devicecheck-11.1-py2.py3-none-any.whl", hash = "sha256:8edb36329cdd5d55e2c2c57c379cb5ba1f500f74a08fe8d2612b1a69b7a26435", size = 3668, upload-time = "2025-06-14T20:48:55.098Z" },
]

[[package]]
name = "pyobjc-framework-devicediscoveryextension"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/9a/b8/102863bfa2f1e414c88bb9f51151a9a58b99c268a841b59d46e0dcc5fe6d/pyobjc_framework_devicediscoveryextension-11.1.tar.gz", hash = "sha256:ae160ea40f25d3ee5e7ce80ac9c1b315f94d0a4c7ccb86920396f71c6bf799a0", size = 14298, upload-time = "2025-06-14T20:57:20.738Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/67/89/fce0c0c89746f399d13e08b40fc12e29a2495f4dcebd30893336d047af18/pyobjc_framework_devicediscoveryextension-11.1-py2.py3-none-any.whl", hash = "sha256:96e5b13c718bd0e6c80fbd4e14b8073cffc88b3ab9bb1bbb4dab7893a62e4f11", size = 4249, upload-time = "2025-06-14T20:48:55.895Z" },
]

[[package]]
name = "pyobjc-framework-dictionaryservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/d6/13/c46f6db61133fee15e3471f33a679da2af10d63fa2b4369e0cd476988721/pyobjc_framework_dictionaryservices-11.1.tar.gz", hash = "sha256:39c24452d0ddd037afeb73a1742614c94535f15b1c024a8a6cc7ff081e1d22e7", size = 10578, upload-time = "2025-06-14T20:57:21.392Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6c/86/4e757b4064a0feb8d60456672560adad0bb5df530ba6621fe65d175dbd90/pyobjc_framework_dictionaryservices-11.1-py2.py3-none-any.whl", hash = "sha256:92f4871066653f18e2394ac93b0a2ab50588d60020f6b3bd93e97b67cd511326", size = 3913, upload-time = "2025-06-14T20:48:56.806Z" },
]

[[package]]
name = "pyobjc-framework-discrecording"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a5/b2/d8d1a28643c2ab681b517647bacb68496c98886336ffbd274f0b2ad28cdc/pyobjc_framework_discrecording-11.1.tar.gz", hash = "sha256:37585458e363b20bb28acdb5cc265dfca934d8a07b7baed2584953c11c927a87", size = 123004, upload-time = "2025-06-14T20:57:22.01Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5e/17/032fa44bb66b6a20c432f3311072f88478b42dcf39b21ebb6c3bbdf2954f/pyobjc_framework_discrecording-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:e29bc8c3741ae52fae092f892de856dbab2363e71537a8ae6fd026ecb88e2252", size = 14581, upload-time = "2025-06-14T20:48:59.228Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/55/d4/a9e2fa7aa38b4ecca9668b3ae9ae4244bf335974c42b46313c3ec631c73a/pyobjc_framework_discrecording-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2d18158366d124852ad58291954611ebdcc43263a3bb75d7fd273408e67720e2", size = 14592, upload-time = "2025-06-14T20:49:00.002Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5e/3c/660d06446b8e67121b755aeb20ba369234845675d25c658127e43fdbc835/pyobjc_framework_discrecording-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b027eca3a0391196d4335fcbd50c03ef1e8f5ce095411ed51a081328b4945bf5", size = 14763, upload-time = "2025-06-14T20:49:00.742Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/31/bb/a1b694e9649b5148254325b3f78d658bb4919fc8d0d1c20c85313178b3da/pyobjc_framework_discrecording-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9cb36715bebdbbe1ad95e3c17359c2f5d3f6479a26b527ea1032154ca7cf3e09", size = 14623, upload-time = "2025-06-14T20:49:01.509Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/62/25/e2552e4e8de09d8e8fe53f87cc0878c3cf2ff2030a6352a22d45a0484be8/pyobjc_framework_discrecording-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:7c33421d6bed0993d9f1861dbf38b717b9a9e49dfb98fdf8b3cd8d558fdd50eb", size = 14799, upload-time = "2025-06-14T20:49:02.251Z" },
]

[[package]]
name = "pyobjc-framework-discrecordingui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-discrecording" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/25/53/d71717f00332b8fc3d8a5c7234fdc270adadfeb5ca9318a55986f5c29c44/pyobjc_framework_discrecordingui-11.1.tar.gz", hash = "sha256:a9f10e2e7ee19582c77f0755ae11a64e3d61c652cbd8a5bf52756f599be24797", size = 19370, upload-time = "2025-06-14T20:57:22.791Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4a/a6/505af43f7a17e0ca3d45e099900764e8758e0ca65341e894b74ade513556/pyobjc_framework_discrecordingui-11.1-py2.py3-none-any.whl", hash = "sha256:33233b87d7b85ce277a51d27acca0f5b38485cf1d1dc8e28a065910047766ee2", size = 4721, upload-time = "2025-06-14T20:49:03.737Z" },
]

[[package]]
name = "pyobjc-framework-diskarbitration"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/da/2a/68fa0c99e04ec1ec24b0b7d6f5b7ec735d5e8a73277c5c0671438a69a403/pyobjc_framework_diskarbitration-11.1.tar.gz", hash = "sha256:a933efc6624779a393fafe0313e43378bcae2b85d6d15cff95ac30048c1ef490", size = 19866, upload-time = "2025-06-14T20:57:23.435Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1f/72/9534ca88effbf2897e07b722920b3f10890dbc780c6fff1ab4893ec1af10/pyobjc_framework_diskarbitration-11.1-py2.py3-none-any.whl", hash = "sha256:6a8e551e54df481a9081abba6fd680f6633babe5c7735f649731b22896bb6f08", size = 4849, upload-time = "2025-06-14T20:49:04.513Z" },
]

[[package]]
name = "pyobjc-framework-dvdplayback"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/b8/76/77046325b1957f0cbcdf4f96667496d042ed4758f3413f1d21df5b085939/pyobjc_framework_dvdplayback-11.1.tar.gz", hash = "sha256:b44c36a62c8479e649133216e22941859407cca5796b5f778815ef9340a838f4", size = 64558, upload-time = "2025-06-14T20:57:24.118Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/59/0c/f0fefa171b6938010d87194e26e63eea5c990c33d2d7828de66802f57c36/pyobjc_framework_dvdplayback-11.1-py2.py3-none-any.whl", hash = "sha256:6094e4651ea29540ac817294b27e1596b9d1883d30e78fb5f9619daf94ed30cb", size = 8221, upload-time = "2025-06-14T20:49:05.297Z" },
]

[[package]]
name = "pyobjc-framework-eventkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/c4/cbba8f2dce13b9be37ecfd423ba2b92aa3f209dbb58ede6c4ce3b242feee/pyobjc_framework_eventkit-11.1.tar.gz", hash = "sha256:5643150f584243681099c5e9435efa833a913e93fe9ca81f62007e287349b561", size = 75177, upload-time = "2025-06-14T20:57:24.81Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/05/0a/384b9ff4c6380cac310cb7b92c145896c20a690192dbfc07b38909787ded/pyobjc_framework_eventkit-11.1-py2.py3-none-any.whl", hash = "sha256:c303207610d9c742f4090799f60103cede466002f3c89cf66011c8bf1987750b", size = 6805, upload-time = "2025-06-14T20:49:06.147Z" },
]

[[package]]
name = "pyobjc-framework-exceptionhandling"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/19/0d/c72a885b40d28a99b586447f9ea6f400589f13d554fcd6f13a2c841bb6d2/pyobjc_framework_exceptionhandling-11.1.tar.gz", hash = "sha256:e010f56bf60ab4e9e3225954ebb53e9d7135d37097043ac6dd2a3f35770d4efa", size = 17890, upload-time = "2025-06-14T20:57:25.521Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7f/81/dde9c73bf307b62c2d605fc818d3e49f857f39e0841766093dbc9ea47b08/pyobjc_framework_exceptionhandling-11.1-py2.py3-none-any.whl", hash = "sha256:31e6538160dfd7526ac0549bc0fce5d039932aea84c36abbe7b49c79ffc62437", size = 7078, upload-time = "2025-06-14T20:49:07.713Z" },
]

[[package]]
name = "pyobjc-framework-executionpolicy"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/0b/cf/54431846508c5d5bb114a415ebb96187da5847105918169e42f4ca3b00e6/pyobjc_framework_executionpolicy-11.1.tar.gz", hash = "sha256:3280ad2f4c5eaf45901f310cee0c52db940c0c63e959ad082efb8df41055d986", size = 13496, upload-time = "2025-06-14T20:57:26.173Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a6/d2/cb192d55786d0f881f2fb60d45b61862a1fcade945f6a7a549ed62f47e61/pyobjc_framework_executionpolicy-11.1-py2.py3-none-any.whl", hash = "sha256:7d4141e572cb916e73bb34bb74f6f976a8aa0a396a0bffd1cf66e5505f7c76c8", size = 3719, upload-time = "2025-06-14T20:49:08.521Z" },
]

[[package]]
name = "pyobjc-framework-extensionkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/ce/7d/89adf16c7de4246477714dce8fcffae4242778aecd0c5f0ad9904725f42c/pyobjc_framework_extensionkit-11.1.tar.gz", hash = "sha256:c114a96f13f586dbbab8b6219a92fa4829896a645c8cd15652a6215bc8ff5409", size = 19766, upload-time = "2025-06-14T20:57:27.106Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/90/2a/93105b5452d2ff680a47e38a3ec6f2a37164babd95e0ab976c07984366de/pyobjc_framework_extensionkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d505a64617c9db4373eb386664d62a82ba9ffc909bffad42cb4da8ca8e244c66", size = 7914, upload-time = "2025-06-14T20:49:11.842Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b8/67/1dbd000d9d0c17d838c471dbb48229fca1ca18fad8453c19ecc01d3312a1/pyobjc_framework_extensionkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:abbadbea5b18e4a6944c3c428753ee298a133cbf601c70e9586b14e3aebf649b", size = 7927, upload-time = "2025-06-14T20:49:12.542Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fb/35/e5d1e633ad5b0c5163afd19ac0b02740e47a45de78d6f2599de3bc6542a5/pyobjc_framework_extensionkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:5c2e203cb8134be1dd7df73d74c630adbaaf43d78eba04be451ea4f8bf582e22", size = 8069, upload-time = "2025-06-14T20:49:13.228Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9f/18/4c5ad3cbbf4f984f5316c2264789080d3caeaae47293cc739a59814f682f/pyobjc_framework_extensionkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3507f67dd06285c09bbdf5216a1148f5dd3a2f10eee7a9318dd14430bf6e67ee", size = 7974, upload-time = "2025-06-14T20:49:14.055Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/75/1b/84ac20bb341a739681ad46ea0ec3d83b40f4716fa6ed966ad93274abe423/pyobjc_framework_extensionkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2767635e57b277e051719fa53c7683396ebdbcf3d40d44c1296758978ca8c92a", size = 8122, upload-time = "2025-06-14T20:49:14.76Z" },
]

[[package]]
name = "pyobjc-framework-externalaccessory"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/d9/a3/519242e6822e1ddc9e64e21f717529079dbc28a353474420da8315d0a8b1/pyobjc_framework_externalaccessory-11.1.tar.gz", hash = "sha256:50887e948b78a1d94646422c243ac2a9e40761675e38b9184487870a31e83371", size = 23123, upload-time = "2025-06-14T20:57:27.845Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7d/1b/e2def12aca9162b0fe0bbf0790d35595d46b2ef12603749c42af9234ffca/pyobjc_framework_externalaccessory-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:00caf75b959db5d14118d78c04085e2148255498839cdee735a0b9f6ef86b6a2", size = 8903, upload-time = "2025-06-14T20:49:18.393Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/6f/1340c193c30ade7b0394b2c8f29f3e6dd501eb23a416a728cc9a23efaec2/pyobjc_framework_externalaccessory-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:50b796a4721db87863a28cd55668cb1547fcc28834afda2032e500cdab5b3d95", size = 8915, upload-time = "2025-06-14T20:49:19.076Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ec/27/1617435d3827a544c2ed2660ecd2e317c82cc8e819a55daa491973349e58/pyobjc_framework_externalaccessory-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:569124b686569c48e3855fff128f438a2b46af06280eac2a516aaa214ad325de", size = 9080, upload-time = "2025-06-14T20:49:19.772Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5b/cf/b825117308f1dcd82c7484d5ee7e3c9a2a00cd39b5bc2a73e43fd9803ceb/pyobjc_framework_externalaccessory-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:318772e698c6363e8c3c81229d93b639f5066a02a742ba1ab10cfdef3101d88b", size = 8961, upload-time = "2025-06-14T20:49:20.472Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a2/25/2b9aefc07e06df08501fbd3f3dc1da555e0943e9e169b842b6ac52505907/pyobjc_framework_externalaccessory-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:d259724665617fc4f3e666d353b756a67cabb74e6f9d7b8f6f250a2d4bf05cb7", size = 9135, upload-time = "2025-06-14T20:49:21.149Z" },
]

[[package]]
name = "pyobjc-framework-fileprovider"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/1b/80/3ebba2c1e5e3aeae989fe038c259a93e7e7e18fd56666ece514d000d38ea/pyobjc_framework_fileprovider-11.1.tar.gz", hash = "sha256:748ca1c75f84afdf5419346a24bf8eec44dca071986f31f00071dc191b3e9ca8", size = 91696, upload-time = "2025-06-14T20:57:28.546Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/49/b2/859d733b0110e56511478ba837fd8a7ba43aa8f8c7e5231b9e3f0258bfbf/pyobjc_framework_fileprovider-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ce6092dfe74c78c0b2abc03bfc18a0f5d8ddc624fc6a1d8dfef26d7796653072", size = 19622, upload-time = "2025-06-14T20:49:24.162Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/91/ed/ae5ce4a18752ea2da5d7238f7847119af8c7dc69ffd9fb1369414c9745d2/pyobjc_framework_fileprovider-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9af41255df395a40a6e0b08c4410be5463f3ea91d8c9be61f6bd114252490ab2", size = 19627, upload-time = "2025-06-14T20:49:24.926Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/84/83/530daae946318689d29457da995577996de5965ff41b4b3b8b604617ff46/pyobjc_framework_fileprovider-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d2720acdd582756ebda34418981e7646b7b85588b0b8fdafba7016eb657be6b8", size = 19859, upload-time = "2025-06-14T20:49:26.008Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e2/de/8411450fc602f841c7001651fc71487de6fc4d418beb5b83a576c734b0e5/pyobjc_framework_fileprovider-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0e48015bf50b3e56312c640ec6efde73cf3855e29b6d70d173a88957d9d74d27", size = 19970, upload-time = "2025-06-14T20:49:26.787Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d9/51/65d9be84e8c33c0341ed79392e9b9896a1f3ca21d96271d293389a94f264/pyobjc_framework_fileprovider-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:95ed3a03741076a4479aabb616b1e3ea022025a0ad842147a1200c27709019e2", size = 20211, upload-time = "2025-06-14T20:49:27.605Z" },
]

[[package]]
name = "pyobjc-framework-fileproviderui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-fileprovider" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/75/ed/0f5af06869661822c4a70aacd674da5d1e6b6661240e2883bbc7142aa525/pyobjc_framework_fileproviderui-11.1.tar.gz", hash = "sha256:162a23e67f59e1bb247e84dda88d513d7944d815144901a46be6fe051b6c7970", size = 13163, upload-time = "2025-06-14T20:57:29.568Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/62/01/667e139a0610494e181fccdce519f644166f3d8955b330674deba5876f0d/pyobjc_framework_fileproviderui-11.1-py2.py3-none-any.whl", hash = "sha256:f2765f114c2f4356aa41fb45c621fa8f0a4fae0b6d3c6b1a274366f5fe7fe829", size = 3696, upload-time = "2025-06-14T20:49:29.404Z" },
]

[[package]]
name = "pyobjc-framework-findersync"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/2a/82/c6b670494ac0c4cf14cf2db0dfbe0df71925d20595404939383ddbcc56d3/pyobjc_framework_findersync-11.1.tar.gz", hash = "sha256:692364937f418f0e4e4abd395a09a7d4a0cdd55fd4e0184de85ee59642defb6e", size = 15045, upload-time = "2025-06-14T20:57:30.173Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/61/10/748ff914c5b7fbae5fa2436cd44b11caeabb8d2f6f6f1b9ab581f70f32af/pyobjc_framework_findersync-11.1-py2.py3-none-any.whl", hash = "sha256:c72b0fd8b746b99cfa498da36c5bb333121b2080ad73fa8cbea05cd47db1fa82", size = 4873, upload-time = "2025-06-14T20:49:30.194Z" },
]

[[package]]
name = "pyobjc-framework-fsevents"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/8e/83/ec0b9ba355dbc34f27ed748df9df4eb6dbfdd9bbd614b0f193752f36f419/pyobjc_framework_fsevents-11.1.tar.gz", hash = "sha256:d29157d04124503c4dfa9dcbbdc8c34d3bab134d3db3a48d96d93f26bd94c14d", size = 29587, upload-time = "2025-06-14T20:57:30.796Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/13/c7/378d78e0fd956370f2b120b209117384b5b98925c6d8210a33fd73db4a15/pyobjc_framework_fsevents-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8b51d120b8f12a1ca94e28cf74113bf2bfd4c5aee7035b452e895518f4df7630", size = 13147, upload-time = "2025-06-14T20:49:33.022Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/dc/3b7e75b9f8284257740679509b54f61da2a114cf805d7d3523053e4c6c19/pyobjc_framework_fsevents-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fad5ada269f137afabd622b5fc04884c668ae1c7914a8791bab73b1d972f7713", size = 13164, upload-time = "2025-06-14T20:49:33.751Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/dd/53/07d62a8642bfddee43cd96301abeed97e858757d363423cf6e383d91f900/pyobjc_framework_fsevents-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ff064cfa9d9cffb5d4ab476fb5091604568744d961c670aced037b2b6f0d0185", size = 13525, upload-time = "2025-06-14T20:49:34.492Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/54/1c/529de91b3ec8f8efc4bb3067678b3071f255637b17168e1d6f0132a8d729/pyobjc_framework_fsevents-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9191ee2819f1d5dcae1559e4a66f19be03da3a103bccdc417e6888bcb5659f8f", size = 13047, upload-time = "2025-06-14T20:49:35.204Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/67/21/f4e72a3761510abe93c089aa77b1f01bc1018ff47df1d09f430de9e1aea5/pyobjc_framework_fsevents-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3289192f4d60e5b26f8ac88ae4049a11eff47caa6fb76ce34e3f7df405119905", size = 13501, upload-time = "2025-06-14T20:49:35.93Z" },
]

[[package]]
name = "pyobjc-framework-fskit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/46/47/d1f04c6115fa78936399a389cc5e0e443f8341c9a6c1c0df7f6fdbe51286/pyobjc_framework_fskit-11.1.tar.gz", hash = "sha256:9ded1eab19b4183cb04381e554bbbe679c1213fd58599d6fc6e135e93b51136f", size = 42091, upload-time = "2025-06-14T20:57:31.504Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/59/8f/db8f03688db77bfa4b78e89af1d89e910c5e877e94d58bdb3e93cc302e5d/pyobjc_framework_fskit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1e50b8f949f1386fada73b408463c87eb81ef7fd0b3482bacf0c206a73723013", size = 19948, upload-time = "2025-06-14T20:49:39.18Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7a/31/0dd6ad9dfce080d6e567326fe7243261740ef1090f72409322040f55a426/pyobjc_framework_fskit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:cc2390934a23b6407aa7802b11978374301444c3135835ad3373f7b4930c24eb", size = 19959, upload-time = "2025-06-14T20:49:39.941Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/96/ba/8655c5959e28fc8b1806a0e0c0b6a47b615de586990efc8ff82a344177a3/pyobjc_framework_fskit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:44fe7b6781c8fd0552b13ab3d0ec21176cd7cd685a8a61d712f9e4e42eb2f736", size = 20201, upload-time = "2025-06-14T20:49:40.715Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/ab/f576e3b078a3afe7930f6dbf8614d91ab08c3574bef970079c679c09c2e0/pyobjc_framework_fskit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:1d3793938e6d9b871483d4a6fad8f93d554bcbebd1fe7bed20e3f5d2feaa814b", size = 20166, upload-time = "2025-06-14T20:49:41.826Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6d/b2/42f72c4e6b0d61a393e66ea921c451bdfdfd6043cf24ae509018b336dbfb/pyobjc_framework_fskit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e38f9c449647109e5b14dc4a17f425efca10c7e539a3836ebdd1f9c0ef725a3b", size = 20437, upload-time = "2025-06-14T20:49:42.585Z" },
]

[[package]]
name = "pyobjc-framework-gamecenter"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/1b/8e/b594fd1dc32a59462fc68ad502be2bd87c70e6359b4e879a99bcc4beaf5b/pyobjc_framework_gamecenter-11.1.tar.gz", hash = "sha256:a1c4ed54e11a6e4efba6f2a21ace92bcf186e3fe5c74a385b31f6b1a515ec20c", size = 31981, upload-time = "2025-06-14T20:57:32.192Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/99/52/0e56f21a6660a4f43882ec641b9e19b7ea92dc7474cec48cda1c9bed9c49/pyobjc_framework_gamecenter-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:779cdf8f52348be7f64d16e3ea37fd621d5ee933c032db3a22a8ccad46d69c59", size = 18634, upload-time = "2025-06-14T20:49:45.737Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3e/fc/64a1e9dc4874a75ceed6e70bb07d5e2a3460283c7737e639a0408ec1b365/pyobjc_framework_gamecenter-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6ff8905a5a7bfd86cb2b95671b452be0836f79db065b8d8b3bb2a1a5750ffd0d", size = 18638, upload-time = "2025-06-14T20:49:46.826Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d5/0b/5a8559056ee1cd2fea7405d3843de900b410a14134c33eb112b9fa42201d/pyobjc_framework_gamecenter-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a73ca7027b2b827e26075b46551fe42425d4a68985022baa4413329a3a2c16ff", size = 18920, upload-time = "2025-06-14T20:49:47.61Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/65/3a/b704f516ef405cb8911afd826fe775af6e06e22ce72bdd0e6c692e303b25/pyobjc_framework_gamecenter-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2a2cb6471d4d4b19f124c7e91a32882a0fab6e326bb0415915fd8f3b91cfc311", size = 18808, upload-time = "2025-06-14T20:49:48.354Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/c9/4759a330d40d10810b5ebf06286d44088e7c0ef5e4e5523d32045cc93495/pyobjc_framework_gamecenter-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:90132bb32f5ed6607e13c6f39346ad621611cb92cea308ced661a6ba1305b94e", size = 19093, upload-time = "2025-06-14T20:49:49.133Z" },
]

[[package]]
name = "pyobjc-framework-gamecontroller"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/70/4c/1dd62103092a182f2ab8904c8a8e3922d2b0a80a7adab0c20e5fd0207d75/pyobjc_framework_gamecontroller-11.1.tar.gz", hash = "sha256:4d5346faf90e1ebe5602c0c480afbf528a35a7a1ad05f9b49991fdd2a97f105b", size = 115783, upload-time = "2025-06-14T20:57:32.879Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/40/e3/e35bccb0284046ef716db4897b70d061b8b16c91fb2c434b1e782322ef56/pyobjc_framework_gamecontroller-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d2cbc0c6c7d9c63e6b5b0b124d0c2bad01bb4b136f3cbc305f27d31f8aab6083", size = 20850, upload-time = "2025-06-14T20:49:52.401Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ae/eb/42469724725f5d0f11c197aadbb0c5db1647ba69579df4e8d13f553bed1c/pyobjc_framework_gamecontroller-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:4866b25df05f583af06095e7103ddd2fbb2484b0ac2c78fd2cd825f995e524fa", size = 20862, upload-time = "2025-06-14T20:49:53.47Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c3/43/7430884d24989c07e4e9394c905b02b3aedee7397960dd329a3c44e29c22/pyobjc_framework_gamecontroller-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:98f3f7afcbbe473a53537da42b2cdc0363df2647289eb66e8c762e4b46c23e73", size = 21108, upload-time = "2025-06-14T20:49:54.226Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/69/55/5eb0027bfa985125ca152dd9720aec8e6d580689cc23326bc1a749c68133/pyobjc_framework_gamecontroller-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:de3892b8d09a65a3413d85a2f0762eba092afda8d97cbf9cda0417689cfb7027", size = 21281, upload-time = "2025-06-14T20:49:54.981Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7f/4f/8c32cf541b972a72e158bcdd1eb95f3180f2eb4532eee9fde8bc58f6961e/pyobjc_framework_gamecontroller-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:afe9f3aed8c900ebe63ee4f6e53c73c2fef7e503f6388afd39f46b31487f84a3", size = 21531, upload-time = "2025-06-14T20:49:55.749Z" },
]

[[package]]
name = "pyobjc-framework-gamekit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/5b/7b/ba141ec0f85ca816f493d1f6fe68c72d01092e5562e53c470a0111d9c34b/pyobjc_framework_gamekit-11.1.tar.gz", hash = "sha256:9b8db075da8866c4ef039a165af227bc29393dc11a617a40671bf6b3975ae269", size = 165397, upload-time = "2025-06-14T20:57:33.711Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1f/23/094e4fe38f2de029365604f0b7dffde7b0edfc57c3d388294c20ed663de2/pyobjc_framework_gamekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f945c7cfe53c4a349a03a1272f2736cc5cf88fe9e7a7a407abb03899635d860c", size = 21952, upload-time = "2025-06-14T20:49:58.933Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/22/2c/9a35fb83a1df7588e2e60488aa425058ee7f01b5a9d4947f74f62a130bf3/pyobjc_framework_gamekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8c7f2bf7ecf44ca678cfdf76f23b32d9c2d03006a0af9ad8e60d9114d6be640a", size = 21968, upload-time = "2025-06-14T20:49:59.688Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7f/23/205eb0532238e79a56bab54820b0e39aedc546429e054dc12d55ca44bb23/pyobjc_framework_gamekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a7c8fce8a2c4614e3dd88b002540e67423e3efd41aa26d576db2de0fc61651b9", size = 22246, upload-time = "2025-06-14T20:50:00.462Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/17/49/f297db34e3cdea78b03ec05bcf280b5afcefe7cb3b674705ca5705ee8bf1/pyobjc_framework_gamekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:555cb8d868fd2699ad70d4f9e7efccaa5df1995893050d05d478cb8f24dbf876", size = 22171, upload-time = "2025-06-14T20:50:01.723Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/85/6e/5c886206d9b34870b66224e1a953afa431dd0c1247d29e5ae0606d06ad33/pyobjc_framework_gamekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:10331a69282b9554ce7ae618dc9ff68e96451759f6cfc687e188c82ba6b0e2ff", size = 22472, upload-time = "2025-06-14T20:50:02.814Z" },
]

[[package]]
name = "pyobjc-framework-gameplaykit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-spritekit" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e0/07/f38b1d83eac10ea4f75c605ffc4850585740db89b90842d311e586ee36cd/pyobjc_framework_gameplaykit-11.1.tar.gz", hash = "sha256:9ae2bee69b0cc1afa0e210b4663c7cdbb3cc94be1374808df06f98f992e83639", size = 73399, upload-time = "2025-06-14T20:57:34.538Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e7/f5/65bdbefb9de7cbc2edf0b1f76286736536e31c216cfac1a5f84ea15f0fc1/pyobjc_framework_gameplaykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:0e4f34db8177b8b4d89fd22a2a882a6c9f6e50cb438ea2fbbf96845481bcd80d", size = 13091, upload-time = "2025-06-14T20:50:05.962Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/25/4c/011e20a8e9ff1270d3efb6c470c3cd8af10dcd2b05042721b1a777aca7a6/pyobjc_framework_gameplaykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:78c513bc53bafd996d896f6f4535f2700b4916013417f8b41f47045790c6208d", size = 13109, upload-time = "2025-06-14T20:50:06.7Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/50/a1/31a50e79dfb9983b53220d0a1148a05544062829af76a20febfa2def0b41/pyobjc_framework_gameplaykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:30e15e4e8df9b1c0ca92bfabf79f6b12a286e544e67762b14dd3023c53e41978", size = 13316, upload-time = "2025-06-14T20:50:07.431Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8d/8c/240c75848df95c29ce1c8aec1e2ac163f0405bcd6456c55075e438fbc92d/pyobjc_framework_gameplaykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4dbea3471b5d4a82b37ddca41bfddd63380c31050de7392e2467fabebcd110b8", size = 13122, upload-time = "2025-06-14T20:50:08.172Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9c/1a/6590c96f57cda822620e66d8e21b5e55a62b14d040f38b0920f21645109e/pyobjc_framework_gameplaykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:51abecafc1b55fcc9a5d73c078ea2d5a75964e0facf2c867a25d7f4f40238331", size = 13333, upload-time = "2025-06-14T20:50:09.468Z" },
]

[[package]]
name = "pyobjc-framework-healthkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/af/66/fa76f7c8e36e4c10677d42d91a8e220c135c610a06b759571db1abe26a32/pyobjc_framework_healthkit-11.1.tar.gz", hash = "sha256:20f59bd9e1ffafe5893b4eff5867fdfd20bd46c3d03bc4009219d82fc6815f76", size = 202009, upload-time = "2025-06-14T20:57:35.285Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c7/08/12fca070ad2dc0b9c311df209b9b6d275ee192cb5ccbc94616d9ddd80d88/pyobjc_framework_healthkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ab4350f9fe65909107dd7992b367a6c8aac7dc31ed3d5b52eeb2310367d0eb0b", size = 20311, upload-time = "2025-06-14T20:50:13.271Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5d/26/0337f1b4607a3a13a671a6b07468726943e0d28a462998fcd902f7df6fbf/pyobjc_framework_healthkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8b6c739e17362897f0b1ba4aa4dc395b3d0c3855b87423eaeb6a89f910adc43f", size = 20330, upload-time = "2025-06-14T20:50:14.042Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f4/da/8681afc37504797f747c45be6780f2ef12b9c2a7703cda8f8cf9e48918ca/pyobjc_framework_healthkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2d1b76b04e9e33ac9441cafa695766938eac04f8c8c69f7efd93a6aceb6eca40", size = 20502, upload-time = "2025-06-14T20:50:14.788Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2e/7a/d8e9db3de92e432340d2b7c65dabace75650d426186658606acb5babc7c1/pyobjc_framework_healthkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:547ac283f84b5024be75290f351863f86eb48a950ec61e3150760230e6eba773", size = 20376, upload-time = "2025-06-14T20:50:15.536Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9d/9f/0ff955096171e5d7d57ca0b879b8771f52cd0f1d4cf0726cdfc0064884f3/pyobjc_framework_healthkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c693725d8476b745232df90ef01487e75e1e1c448e599dd34adf3dce859de760", size = 20544, upload-time = "2025-06-14T20:50:16.263Z" },
]

[[package]]
name = "pyobjc-framework-imagecapturecore"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/7b/3b/f4edbc58a8c7394393f8d00d0e764f655545e743ee4e33917f27b8c68e7b/pyobjc_framework_imagecapturecore-11.1.tar.gz", hash = "sha256:a610ceb6726e385b132a1481a68ce85ccf56f94667b6d6e1c45a2cfab806a624", size = 100398, upload-time = "2025-06-14T20:57:36.503Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/61/62/54ed61e7cd3213549c8e98ca87a6b21afbb428d2c41948ae48ea019bf973/pyobjc_framework_imagecapturecore-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ed296c23d3d8d1d9af96a6486d09fb8d294cc318e4a2152e6f134151c76065f8", size = 16021, upload-time = "2025-06-14T20:50:19.836Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4e/91/71d48ec1b29d57112edd33ada86fcdbf1c9423ef2bdddadf8d37e8a03492/pyobjc_framework_imagecapturecore-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ded8dc6a8c826a6ae1b6a6d0a31542bd1eb85345f86201689c54e51193b572dc", size = 16030, upload-time = "2025-06-14T20:50:20.568Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c7/9d/7452fecf9b362b7a384b44256ca388b3e99905376e6f594565f2b2be0761/pyobjc_framework_imagecapturecore-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:254ae4502d651526c500533b8e2aee77ae7939f9acfd7d706dba2d464417deba", size = 16234, upload-time = "2025-06-14T20:50:21.341Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f9/37/b7207fd6f8d9b55d642ad73850148ae68c4877f993c5ae2f7eac2578b991/pyobjc_framework_imagecapturecore-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bab8ed798598ddaa53f5b39707b58e16a1b1152858c87fd3fa0d64081f0c0364", size = 16115, upload-time = "2025-06-14T20:50:22.092Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6d/06/6eb5f2b1e2c8716ed07560055544f752ead2c2773dfc85cb24d9ec429b0e/pyobjc_framework_imagecapturecore-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e01c29456d0560667f8fcd3ff2749e79ad51bf72512e699646ce32227f91b447", size = 16279, upload-time = "2025-06-14T20:50:22.82Z" },
]

[[package]]
name = "pyobjc-framework-inputmethodkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/02/32/6a90bba682a31960ba1fc2d3b263e9be26043c4fb7aed273c13647c8b7d9/pyobjc_framework_inputmethodkit-11.1.tar.gz", hash = "sha256:7037579524041dcee71a649293c2660f9359800455a15e6a2f74a17b46d78496", size = 27203, upload-time = "2025-06-14T20:57:37.246Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a8/0d/8a570072096fe339702e4ae9d98e59ee7c6c14124d4437c9a8c4482dda6d/pyobjc_framework_inputmethodkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dd0c591a9d26967018a781fa4638470147ef2a9af3ab4a28612f147573eeefba", size = 9489, upload-time = "2025-06-14T20:50:25.875Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/dc/a5/ce000bba1a52287c21d1d3aff6779a6bbb463da4337573cb17ecc9475939/pyobjc_framework_inputmethodkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:5095005809a4108f362998b46994f99b5a57f9ba367c01141c1b9eaea311bc5b", size = 9508, upload-time = "2025-06-14T20:50:26.577Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/56/ad/bbdc9f4b91420a4d3cf0b633d1991d4ffb7bdeb78d01fa265bbd43fef929/pyobjc_framework_inputmethodkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:013919a4d766a7e66045fa5dd5d819bfa0450ccb59baba2b89d7449bce637d6b", size = 9667, upload-time = "2025-06-14T20:50:27.617Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/13/92/d69e350213c242a2096f5708692effda0a0c96aab07410ecf582591b6f7f/pyobjc_framework_inputmethodkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2228bf58369351767294fe1aa400e98ec61e397a74a178788c24c98a1cff97ee", size = 9517, upload-time = "2025-06-14T20:50:28.333Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7f/b0/c6ee5412bb402f9c8ac9a0bbd471f4fd57a1d2ca9510480cb67d12ebaa8d/pyobjc_framework_inputmethodkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:92b9ce788ce4b094e352a64508050ff8e24307b8670d33488304b941d118894e", size = 9696, upload-time = "2025-06-14T20:50:29.387Z" },
]

[[package]]
name = "pyobjc-framework-installerplugins"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/4d/89/9a881e466476ca21f3ff3e8e87ccfba1aaad9b88f7eea4be6d3f05b07107/pyobjc_framework_installerplugins-11.1.tar.gz", hash = "sha256:363e59c7e05553d881f0facd41884f17b489ff443d7856e33dd0312064c746d9", size = 27451, upload-time = "2025-06-14T20:57:37.915Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3d/01/45c3d159d671c5f488a40f70aa6791b8483a3ed32b461800990bb5ab4bb3/pyobjc_framework_installerplugins-11.1-py2.py3-none-any.whl", hash = "sha256:f92b06c9595f3c800b7aabf1c1a235bfb4b2de3f5406d5f604d8e2ddd0aecb4e", size = 4798, upload-time = "2025-06-14T20:50:30.799Z" },
]

[[package]]
name = "pyobjc-framework-instantmessage"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/9f/b9/5cec4dd0053b5f63c01211a60a286c47464d9f3e0c81bd682e6542dbff00/pyobjc_framework_instantmessage-11.1.tar.gz", hash = "sha256:c222aa61eb009704b333f6e63df01a0e690136e7e495907e5396882779bf9525", size = 33774, upload-time = "2025-06-14T20:57:38.553Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/91/34/acd618e90036822aaf01080d64558ba93e33e15ed91beb7d1d2aab290138/pyobjc_framework_instantmessage-11.1-py2.py3-none-any.whl", hash = "sha256:a70b716e279135eec5666af031f536c0f32dec57cfeae55cc9ff8457f10d4f3d", size = 5419, upload-time = "2025-06-14T20:50:31.993Z" },
]

[[package]]
name = "pyobjc-framework-intents"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/4c/af/d7f260d06b79acca8028e373c2fe30bf0be014388ba612f538f40597d929/pyobjc_framework_intents-11.1.tar.gz", hash = "sha256:13185f206493f45d6bd2d4903c2136b1c4f8b9aa37628309ace6ff4a906b4695", size = 448459, upload-time = "2025-06-14T20:57:39.589Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8a/37/e6fa5737da42fb1265041bd3bd4f2be96f09294018fabf07139dd9dbc7b9/pyobjc_framework_intents-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a663e2de1b7ae7b547de013f89773963f8180023e36f2cebfe8060395dc34c33", size = 32253, upload-time = "2025-06-14T20:50:35.028Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f0/ff/f793a0c4b5ea87af3fc228d74e457c1594695b2745b3007a8ef4832ebeb7/pyobjc_framework_intents-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9e21b3bc33de2d5f69b5c1d581e5c724a08686fe84ec324a4be365bef769e482", size = 32266, upload-time = "2025-06-14T20:50:35.775Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/52/e9/2725ae5f990faa7d7909e6ac14d14034d1e70028080ed602a03aa715b4bc/pyobjc_framework_intents-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e008d542abe38fd374c9ada7c833ad6e34a2db92b4dcbfba0a59ff830b9093bc", size = 32499, upload-time = "2025-06-14T20:50:36.531Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/90/47/d934ec7c514cc59b53da271f172cf6fd30e9a63aa960580a751d4960d495/pyobjc_framework_intents-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:55498040123904b685cd38555eb84d95833fcb467b497d31757d6ac648a11817", size = 32506, upload-time = "2025-06-14T20:50:37.271Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/95/f1/acbda130f45e38f35fca2aa381f4da9ed72e36c4c784395ddb3fea511391/pyobjc_framework_intents-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e3ec70c02d3166088223938a7433e479659cbd8ce04be5bf515ea8d6e3c353d", size = 32742, upload-time = "2025-06-14T20:50:38.157Z" },
]

[[package]]
name = "pyobjc-framework-intentsui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-intents" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/86/46/20aae4a71efb514b096f36273a6129b48b01535bf501e5719d4a97fcb3a5/pyobjc_framework_intentsui-11.1.tar.gz", hash = "sha256:c8182155af4dce369c18d6e6ed9c25bbd8110c161ed5f1b4fb77cf5cdb99d135", size = 21305, upload-time = "2025-06-14T20:57:40.477Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/43/7c/77fbd2a6f85eb905fbf27ba7540eaf2a026771ed5100fb1c01143cf47e9b/pyobjc_framework_intentsui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:99a3ae40eb2a6ef1125955dd513c8acc88ce7d8d90130a8cdeaec8336e6fbec5", size = 8965, upload-time = "2025-06-14T20:50:41.281Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9b/d6/ce8e2f6354bd77271b8f9f2a05920fb0a6de57ab5d97033021672853acb5/pyobjc_framework_intentsui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:154fd92112184e8ef29ce81e685c377422dffcff4f7900ea6e5956a0e2be2268", size = 8983, upload-time = "2025-06-14T20:50:41.96Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e1/2b/562785a91c30eccd3eea28ea02b31a029e04ecc5e994da7cd60205baf250/pyobjc_framework_intentsui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6d7d5402c05840a45047cf905fa550c2898cf5580cdee00a36bd35dd624c7542", size = 9154, upload-time = "2025-06-14T20:50:42.651Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/94/30/069cf617e514434304ea0b1e8227d653af192c6dc7062f2e97ab0204e449/pyobjc_framework_intentsui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:35ef9f190f480147ce797809a63cc2b5f2ea64b51255d691e5e94bd8337e01ef", size = 9029, upload-time = "2025-06-14T20:50:43.353Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7a/77/6830682e3d7b9fdbead08f9053d714336f1cf5c6c6170d91b9cc266d243f/pyobjc_framework_intentsui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1bd950f808efb7ba7fbbc977300d7932a1dad41fbd3c78c8002870ca602e22d5", size = 9232, upload-time = "2025-06-14T20:50:44.031Z" },
]

[[package]]
name = "pyobjc-framework-iobluetooth"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/93/e0/74b7b10c567b66c5f38b45ab240336325a4c889f43072d90f2b90aaeb7c0/pyobjc_framework_iobluetooth-11.1.tar.gz", hash = "sha256:094fd4be60cd1371b17cb4b33a3894e0d88a11b36683912be0540a7d51de76f1", size = 300992, upload-time = "2025-06-14T20:57:41.256Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/da/94/eef57045762e955795a4e3312674045c52f8c506133acf9efe1b3370b93f/pyobjc_framework_iobluetooth-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:883781e7223cb0c63fab029d640721ded747f2e2b067645bc8b695ef02a4a4dd", size = 40406, upload-time = "2025-06-14T20:50:47.101Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ed/f5/24476d6919c2d8d849c88740e81f620663181b3c97ac6e3aaeb1833277a5/pyobjc_framework_iobluetooth-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:4a8b1caba9ac51435f64a6cf9c1a2be867603161af8bebdd1676072ebed2fed9", size = 40428, upload-time = "2025-06-14T20:50:47.85Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/57/b6/ced1b076a86ea3d7a685155e8c61ab9ecf8037d2b5401d4aae65014789b3/pyobjc_framework_iobluetooth-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2c99ade82a79263ea71c51d430696a2ad155beb01a67df59d52be63e181e0482", size = 40626, upload-time = "2025-06-14T20:50:48.655Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d2/a2/0567b8b6e5bb75f7172495890a7746a986fd46a436e5f1ca7abc386bbbdc/pyobjc_framework_iobluetooth-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2ef72cef1e03468e91a2f01af2390143bd6e4fcad1c6d0494dd857c99fa0d1a7", size = 40478, upload-time = "2025-06-14T20:50:49.418Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/eb/b148fba594890aec937bf3a87b61a385918f2bee4394763595e59a9f39a0/pyobjc_framework_iobluetooth-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a9a7e11a4bbb4a364b0412ca8632a1e853270c98c24d28421133f69c0c0ecaff", size = 40690, upload-time = "2025-06-14T20:50:50.174Z" },
]

[[package]]
name = "pyobjc-framework-iobluetoothui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-iobluetooth" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/dd/32/872272faeab6fe471eac6962c75db72ce65c3556e00b4edebdb41aaab7cb/pyobjc_framework_iobluetoothui-11.1.tar.gz", hash = "sha256:060c721f1cd8af4452493e8153b72b572edcd2a7e3b635d79d844f885afee860", size = 22835, upload-time = "2025-06-14T20:57:42.119Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d6/ed/35efed52ed3fa698480624e49ee5f3d859827aad5ff1c7334150c695e188/pyobjc_framework_iobluetoothui-11.1-py2.py3-none-any.whl", hash = "sha256:3c5a382d81f319a1ab9ab11b7ead04e53b758fdfeb604755d39c3039485eaac6", size = 4026, upload-time = "2025-06-14T20:50:52.018Z" },
]

[[package]]
name = "pyobjc-framework-iosurface"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/c5/ce/38ec17d860d0ee040bb737aad8ca7c7ff46bef6c9cffa47382d67682bb2d/pyobjc_framework_iosurface-11.1.tar.gz", hash = "sha256:a468b3a31e8cd70a2675a3ddc7176ab13aa521c035f11188b7a3af8fff8b148b", size = 20275, upload-time = "2025-06-14T20:57:42.742Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1d/26/fa912d397b577ee318b20110a3c959e898514a1dce19b4f13f238a31a677/pyobjc_framework_iosurface-11.1-py2.py3-none-any.whl", hash = "sha256:0c36ad56f8ec675dd07616418a2bc29126412b54627655abd21de31bcafe2a79", size = 4948, upload-time = "2025-06-14T20:50:52.801Z" },
]

[[package]]
name = "pyobjc-framework-ituneslibrary"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/ee/43/aebefed774b434965752f9001685af0b19c02353aa7a12d2918af0948181/pyobjc_framework_ituneslibrary-11.1.tar.gz", hash = "sha256:e2212a9340e4328056ade3c2f9d4305c71f3f6af050204a135f9fa9aa3ba9c5e", size = 47388, upload-time = "2025-06-14T20:57:43.383Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2a/57/a29150f734b45b7408cc06efb9e2156328ae74624e5c4a7fe95118e13e94/pyobjc_framework_ituneslibrary-11.1-py2.py3-none-any.whl", hash = "sha256:4e87d41f82acb6d98cf70ac3c932a568ceb3c2035383cbf177f54e63de6b815f", size = 5191, upload-time = "2025-06-14T20:50:53.637Z" },
]

[[package]]
name = "pyobjc-framework-kernelmanagement"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/1a/b6/708f10ac16425834cb5f8b71efdbe39b42c3b1009ac0c1796a42fc98cd36/pyobjc_framework_kernelmanagement-11.1.tar.gz", hash = "sha256:e934d1638cd89e38d6c6c5d4d9901b4295acee2d39cbfe0bd91aae9832961b44", size = 12543, upload-time = "2025-06-14T20:57:44.046Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/cf/17ff988ad1a0e55a4be5336c64220aa620ad19bb2f487a1122e9a864b29e/pyobjc_framework_kernelmanagement-11.1-py2.py3-none-any.whl", hash = "sha256:ec74690bd3383a7945c4a038cc4e1553ec5c1d2408b60e2b0003a3564bff7c47", size = 3656, upload-time = "2025-06-14T20:50:54.484Z" },
]

[[package]]
name = "pyobjc-framework-latentsemanticmapping"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/db/8a/4e54ee2bc77d59d770b287daf73b629e2715a2b3b31264d164398131cbad/pyobjc_framework_latentsemanticmapping-11.1.tar.gz", hash = "sha256:c6c3142301e4d375c24a47dfaeebc2f3d0fc33128a1c0a755794865b9a371145", size = 17444, upload-time = "2025-06-14T20:57:44.643Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2c/50/d62815b02968236eb46c33f0fb0f7293a32ef68d2ec50c397140846d4e42/pyobjc_framework_latentsemanticmapping-11.1-py2.py3-none-any.whl", hash = "sha256:57f3b183021759a100d2847a4d8aa314f4033be3d2845038b62e5e823d96e871", size = 5454, upload-time = "2025-06-14T20:50:55.658Z" },
]

[[package]]
name = "pyobjc-framework-launchservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/2b/0a/a76b13109b8ab563fdb2d7182ca79515f132f82ac6e1c52351a6b02896a8/pyobjc_framework_launchservices-11.1.tar.gz", hash = "sha256:80b55368b1e208d6c2c58395cc7bc12a630a2a402e00e4930493e9bace22b7bb", size = 20446, upload-time = "2025-06-14T20:57:45.258Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/12/30/a4de9021fdef7db0b224cdc1eae75811d889dc1debdfafdabf8be7bd0fb9/pyobjc_framework_launchservices-11.1-py2.py3-none-any.whl", hash = "sha256:8b58f1156651058b2905c87ce48468f4799db86a7edf760e1897fedd057a3908", size = 3889, upload-time = "2025-06-14T20:50:56.484Z" },
]

[[package]]
name = "pyobjc-framework-libdispatch"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/be/89/7830c293ba71feb086cb1551455757f26a7e2abd12f360d375aae32a4d7d/pyobjc_framework_libdispatch-11.1.tar.gz", hash = "sha256:11a704e50a0b7dbfb01552b7d686473ffa63b5254100fdb271a1fe368dd08e87", size = 53942, upload-time = "2025-06-14T20:57:45.903Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ac/92/ff9ceb14e1604193dcdb50643f2578e1010c68556711cd1a00eb25489c2b/pyobjc_framework_libdispatch-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dc9a7b8c2e8a63789b7cf69563bb7247bde15353208ef1353fff0af61b281684", size = 15627, upload-time = "2025-06-14T20:50:59.055Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0f/10/5851b68cd85b475ff1da08e908693819fd9a4ff07c079da9b0b6dbdaca9c/pyobjc_framework_libdispatch-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c4e219849f5426745eb429f3aee58342a59f81e3144b37aa20e81dacc6177de1", size = 15648, upload-time = "2025-06-14T20:50:59.809Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1b/79/f905f22b976e222a50d49e85fbd7f32d97e8790dd80a55f3f0c305305c32/pyobjc_framework_libdispatch-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a9357736cb47b4a789f59f8fab9b0d10b0a9c84f9876367c398718d3de085888", size = 15912, upload-time = "2025-06-14T20:51:00.572Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ee/b0/225a3645ba2711c3122eec3e857ea003646643b4122bd98db2a8831740ff/pyobjc_framework_libdispatch-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cd08f32ea7724906ef504a0fd40a32e2a0be4d64b9239530a31767ca9ccfc921", size = 15655, upload-time = "2025-06-14T20:51:01.655Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e2/b5/ff49fb81f13c7ec48cd7ccad66e1986ccc6aa1984e04f4a78074748f7926/pyobjc_framework_libdispatch-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5d9985b0e050cae72bf2c6a1cc8180ff4fa3a812cd63b2dc59e09c6f7f6263a1", size = 15920, upload-time = "2025-06-14T20:51:02.407Z" },
]

[[package]]
name = "pyobjc-framework-libxpc"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/6a/c9/7e15e38ac23f5bfb4e82bdf3b7ef88e2f56a8b4ad884009bc2d5267d2e1f/pyobjc_framework_libxpc-11.1.tar.gz", hash = "sha256:8fd7468aa520ff19915f6d793070b84be1498cb87224bee2bad1f01d8375273a", size = 49135, upload-time = "2025-06-14T20:57:46.59Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/be/8f/dfd8e1e1e461f857a1e50138e69b17c0e62a8dcaf7dea791cc158d2bf854/pyobjc_framework_libxpc-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c29b2df8d74ff6f489afa7c39f7c848c5f3d0531a6bbe704571782ee6c820084", size = 19573, upload-time = "2025-06-14T20:51:05.902Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/00/fa/9ac86892294428a0eb532242a6fcbec565d0cf0e919924b6b7c064c8b196/pyobjc_framework_libxpc-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6862e63f565823d4eeb56f18f90a3ee8682c52a8d4bcd486d3535c9959464eda", size = 19578, upload-time = "2025-06-14T20:51:06.659Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/44/2c/0b0bdc7847adf6ed653e846a98685346f70b1aaa187e37ddff2641cc54e2/pyobjc_framework_libxpc-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2df539d11b65e229f8436a3660d0d1dce2cc7ba571054c5b91350b836db22576", size = 20167, upload-time = "2025-06-14T20:51:07.423Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/13/f0/b44b1b094eafe62d3af6e13098eae1f2a9a863661d3d60745a6a0b91b4c4/pyobjc_framework_libxpc-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4f3083fde3c366cc58bcdb2c183fae9c531fb556d35a495818019f1a5d85c24d", size = 19291, upload-time = "2025-06-14T20:51:08.154Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7f/e4/9b7d86a0aa15ef3b6893238d7634dcfc08b6a800cd61d8a607055224c955/pyobjc_framework_libxpc-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:654db8e822e60a1246d4d55c7127a140e10d6faa0da5a7366a16cc10def44deb", size = 19868, upload-time = "2025-06-14T20:51:09.296Z" },
]

[[package]]
name = "pyobjc-framework-linkpresentation"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/76/22873be73f12a3a11ae57af13167a1d2379e4e7eef584de137156a00f5ef/pyobjc_framework_linkpresentation-11.1.tar.gz", hash = "sha256:a785f393b01fdaada6d7d6d8de46b7173babba205b13b44f1dc884b3695c2fc9", size = 14987, upload-time = "2025-06-14T20:57:47.277Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3d/59/23249e76e06e3c1a4f88acac7144999fae5a5a8ce4b90272d08cc0ac38ae/pyobjc_framework_linkpresentation-11.1-py2.py3-none-any.whl", hash = "sha256:018093469d780a45d98f4e159f1ea90771caec456b1599abcc6f3bf3c6873094", size = 3847, upload-time = "2025-06-14T20:51:10.817Z" },
]

[[package]]
name = "pyobjc-framework-localauthentication"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e5/27/9e3195f3561574140e9b9071a36f7e0ebd18f50ade9261d23b5b9df8fccd/pyobjc_framework_localauthentication-11.1.tar.gz", hash = "sha256:3cd48907c794bd414ac68b8ac595d83c7e1453b63fc2cfc2d2035b690d31eaa1", size = 40700, upload-time = "2025-06-14T20:57:47.931Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/91/db/59f118cc2658814c6b501b7360ca4fe6a82fd289ced5897b99787130ceef/pyobjc_framework_localauthentication-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:aa3815f936612d78e51b53beed9115c57ae2fd49500bb52c4030a35856e6569e", size = 10730, upload-time = "2025-06-14T20:51:13.487Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9f/8b/544cadc6ecf75def347e96cdae4caa955bc23f2bc314779cffe1e6ba9475/pyobjc_framework_localauthentication-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9c9446c017b13c8dcadf485b76ab1d7bc12099b504bf5c2df1aae33b5dc4ab2c", size = 10748, upload-time = "2025-06-14T20:51:14.198Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/44/f9/4095b2caa4453971bd790b6aeda05967c22743e1f80e5bf6cb63ec419288/pyobjc_framework_localauthentication-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d5a2e1ea2fe8233dc244f6029d5d0c878102b2e0615cb4b81b2f30d9ee101fca", size = 10896, upload-time = "2025-06-14T20:51:14.892Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/dd/0a/fd8cfcfd761792fd482b49d08f5a0bf6540ebb3de6baacb4a5de5c5ed635/pyobjc_framework_localauthentication-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f49c9dbbecfa0b0a7a633c60bda8179575e3685b6a696658a835c63afee90f9a", size = 10786, upload-time = "2025-06-14T20:51:15.958Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ec/87/5204ea53e0a945877c650205841f766bc7fca55ad81cd5bcb0a966fcdaa4/pyobjc_framework_localauthentication-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e41be8e2132d1517e597401c7858b22531db2e7760d898993acc03ea13edb834", size = 10930, upload-time = "2025-06-14T20:51:16.696Z" },
]

[[package]]
name = "pyobjc-framework-localauthenticationembeddedui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-localauthentication" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/29/7b/08c1e52487b07e9aee4c24a78f7c82a46695fa883113e3eece40f8e32d40/pyobjc_framework_localauthenticationembeddedui-11.1.tar.gz", hash = "sha256:22baf3aae606e5204e194f02bb205f244e27841ea7b4a4431303955475b4fa56", size = 14076, upload-time = "2025-06-14T20:57:48.557Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/51/3d/2aaa3a4f0e82f0ac95cc432a6079f6dc20aa18a66c9a87ac6128c70df9ef/pyobjc_framework_localauthenticationembeddedui-11.1-py2.py3-none-any.whl", hash = "sha256:3539a947b102b41ea6e40e7c145f27280d2f36a2a9a1211de32fa675d91585eb", size = 3973, upload-time = "2025-06-14T20:51:18.2Z" },
]

[[package]]
name = "pyobjc-framework-mailkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/7e/7e/f22d733897e7618bd70a658b0353f5f897c583df04e7c5a2d68b99d43fbb/pyobjc_framework_mailkit-11.1.tar.gz", hash = "sha256:bf97dc44cb09b9eb9d591660dc0a41f077699976144b954caa4b9f0479211fd7", size = 32012, upload-time = "2025-06-14T20:57:49.173Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/bf/23/1897fc071e8e71bc0bef53bcb0d600eb1ed3bd6c4609f7257ddfe151d37a/pyobjc_framework_mailkit-11.1-py2.py3-none-any.whl", hash = "sha256:8e6026462567baba194468e710e83787f29d9e8c98ea0583f7b401ea9515966e", size = 4854, upload-time = "2025-06-14T20:51:18.978Z" },
]

[[package]]
name = "pyobjc-framework-mapkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-corelocation" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/57/f0/505e074f49c783f2e65ca82174fd2d4348568f3f7281c1b81af816cf83bb/pyobjc_framework_mapkit-11.1.tar.gz", hash = "sha256:f3a5016f266091be313a118a42c0ea4f951c399b5259d93639eb643dacc626f1", size = 165614, upload-time = "2025-06-14T20:57:50.362Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/30/0a/50aa2fba57499ff657cacb9ef1730006442e4f42d9a822dae46239603ecc/pyobjc_framework_mapkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:91976c6dbc8cbb020e059a0ccdeab8933184712f77164dbad5a5526c1a49599d", size = 22515, upload-time = "2025-06-14T20:51:21.439Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/54/792f4d5848176753bfde8f10ac21b663981adf940243765edad45908cd55/pyobjc_framework_mapkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:0b6fa1c4fffc3ae91adb965731a0cc943b3b6e82c8f21919a53a68b43a67b534", size = 22534, upload-time = "2025-06-14T20:51:22.199Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/0c/fd03986fc74c5e523e5ba824d3b4f0fd1f4a52720f28da93499787960317/pyobjc_framework_mapkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:1dc27d315849ac96647d13c82eeefce5d1d2db8c64767ce10bd3e77cbaad2291", size = 22759, upload-time = "2025-06-14T20:51:23.269Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/15/e3/6040945ad0bfb9a065d007a5e16b07f8ae0423fcf4e097eba92eb8a143bb/pyobjc_framework_mapkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:fb9b1d8cd5c0e8a097438369771d296de808621bc6013aa0065bc83716f5bdb0", size = 22657, upload-time = "2025-06-14T20:51:24.01Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e2/07/eca78e240aa13c4e32ac4c6db158e059f375a2d240928e42c8e77f348ef0/pyobjc_framework_mapkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:fe4581f5370dc7a209c1135e9c664a5a78950d3f5c39613bfb15c1e02a6258f3", size = 22886, upload-time = "2025-06-14T20:51:24.803Z" },
]

[[package]]
name = "pyobjc-framework-mediaaccessibility"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/8d/81/60412b423c121de0fa0aa3ef679825e1e2fe8b00fceddec7d72333ef564b/pyobjc_framework_mediaaccessibility-11.1.tar.gz", hash = "sha256:52479a998fec3d079d2d4590a945fc78c41fe7ac8c76f1964c9d8156880565a4", size = 18440, upload-time = "2025-06-14T20:57:51.126Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/99/a1/f4cbdf8478ad01859e2c8eef08e28b8a53b9aa4fe5d238a86bad29b73555/pyobjc_framework_mediaaccessibility-11.1-py2.py3-none-any.whl", hash = "sha256:cd07e7fc375ff1e8d225e0aa2bd9c2c1497a4d3aa5a80bfb13b08800fcd7f034", size = 4691, upload-time = "2025-06-14T20:51:26.596Z" },
]

[[package]]
name = "pyobjc-framework-mediaextension"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e1/09/fd214dc0cf3f3bc3f528815af4799c0cb7b4bf4032703b19ea63486a132b/pyobjc_framework_mediaextension-11.1.tar.gz", hash = "sha256:85a1c8a94e9175fb364c453066ef99b95752343fd113f08a3805cad56e2fa709", size = 58489, upload-time = "2025-06-14T20:57:51.796Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/56/78/2c2d8265851f6060dbf4434c21bd67bf569b8c3071ba1f257e43aae563a8/pyobjc_framework_mediaextension-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:06cb19004413a4b08dd75cf1e5dadea7f2df8d15feeeb7adb529d0cf947fa789", size = 38859, upload-time = "2025-06-14T20:51:29.102Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e7/6b/1d3761316ca7df57700a68b28f7c00cc4f050b3f6debac2305219506d6b1/pyobjc_framework_mediaextension-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:40f1440ccc8da6deb80810866f8c807c17567db67b53e1576ea3a3b1330c85f9", size = 38870, upload-time = "2025-06-14T20:51:29.862Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/15/e3/48f4ba724e31cb7adeaf5f9198ad5ab9cab45bcfc358b8af5759d8f79971/pyobjc_framework_mediaextension-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:29edab42d9ecd394ac26f2ae2dfd7e2118452fc60a5623843919c1e9659c9dbc", size = 39104, upload-time = "2025-06-14T20:51:30.956Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a7/f8/65cfc9e9be245a7524572b64655d809c9294ded599ebf068c7c1b73c6ecf/pyobjc_framework_mediaextension-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5efd284932ed0e7cfbca90a142b84a3966c73e51308688f8c230af41f9fb8c39", size = 38925, upload-time = "2025-06-14T20:51:31.712Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/68/99/bdc2fa27576302b6b3a5b018579637251e4ba4620505254e7ebd79134ad1/pyobjc_framework_mediaextension-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:ca3a3ef1f3a759b53f297ccd701d29091eec66cc629a2b48c9acbe6c297bf256", size = 39142, upload-time = "2025-06-14T20:51:32.844Z" },
]

[[package]]
name = "pyobjc-framework-medialibrary"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/2b/06/11ff622fb5fbdd557998a45cedd2b0a1c7ea5cc6c5cb015dd6e42ebd1c41/pyobjc_framework_medialibrary-11.1.tar.gz", hash = "sha256:102f4326f789734b7b2dfe689abd3840ca75a76fb8058bd3e4f85398ae2ce29d", size = 18706, upload-time = "2025-06-14T20:57:52.474Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/62/2b/a4200080d97f88fdd406119bb8f00ccb7f32794f84735485510c14e87e76/pyobjc_framework_medialibrary-11.1-py2.py3-none-any.whl", hash = "sha256:779be84bd280f63837ce02028ca46b41b090902aa4205887ffd5777f49377669", size = 4340, upload-time = "2025-06-14T20:51:34.339Z" },
]

[[package]]
name = "pyobjc-framework-mediaplayer"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/80/d5/daba26eb8c70af1f3823acfd7925356acc4dd75eeac4fc86dc95d94d0e15/pyobjc_framework_mediaplayer-11.1.tar.gz", hash = "sha256:d07a634b98e1b9eedd82d76f35e616525da096bd341051ea74f0971e0f2f2ddd", size = 93749, upload-time = "2025-06-14T20:57:53.165Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2b/aa/b37aac80d821bd2fa347ddad1f6c7c75b23155e500edf1cb3b3740c27036/pyobjc_framework_mediaplayer-11.1-py2.py3-none-any.whl", hash = "sha256:b655cf537ea52d73209eb12935a047301c30239b318a366600f0f44335d51c9a", size = 6960, upload-time = "2025-06-14T20:51:35.171Z" },
]

[[package]]
name = "pyobjc-framework-mediatoolbox"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e1/68/cc230d2dfdeb974fdcfa828de655a43ce2bf4962023fd55bbb7ab0970100/pyobjc_framework_mediatoolbox-11.1.tar.gz", hash = "sha256:97834addc5179b3165c0d8cd74cc97ad43ed4c89547724216426348aca3b822a", size = 23568, upload-time = "2025-06-14T20:57:53.913Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b5/23/6b5d999e1e71c42d5d116d992515955ac1bbc5cf4890072bb26f38eb9802/pyobjc_framework_mediatoolbox-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2867c91645a335ee29b47e9c0e9fd3ea8c9daad0c0719c50b8bf244d76998056", size = 12785, upload-time = "2025-06-14T20:51:37.593Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/29/05/24d60869a816418771653057720727d6df2dd8485302a21f80cfcb694110/pyobjc_framework_mediatoolbox-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:bf26348d20caef38efb9cfc02d28af83c930b2f2c9581407f8ec04b3d8321a7a", size = 12794, upload-time = "2025-06-14T20:51:38.278Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/37/c5/7b2950c22187c1a2e4f492684c34dd0cd230b8be4c7749e4b223b7769def/pyobjc_framework_mediatoolbox-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:975de470af8e52104bd1548eb9b4b0ef98524f35a6263c0bb4182797b9c5975b", size = 13394, upload-time = "2025-06-14T20:51:39.001Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d8/b4/f3b9944cb80bb5e72f3550ddfe6ba9fca81eefcb75abbf3410b304e0b1ca/pyobjc_framework_mediatoolbox-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d781e45fb1a7e532bcbae38c0f491629eaa641cdc226019544123b51794baf34", size = 12775, upload-time = "2025-06-14T20:51:39.745Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d3/6b/22f33982711fe787b2808530365afa2d4663d231200de51013cccc4cec46/pyobjc_framework_mediatoolbox-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e30fd2ffdea1b2c7c314d07266bce7614197c2b3ffd5b09f7012e7df7aa5c7a6", size = 13379, upload-time = "2025-06-14T20:51:41.235Z" },
]

[[package]]
name = "pyobjc-framework-metal"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/af/cf/29fea96fd49bf72946c5dac4c43ef50f26c15e9f76edd6f15580d556aa23/pyobjc_framework_metal-11.1.tar.gz", hash = "sha256:f9fd3b7574a824632ee9b7602973da30f172d2b575dd0c0f5ef76b44cfe9f6f9", size = 446549, upload-time = "2025-06-14T20:57:54.731Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4c/94/3d5a8bed000dec4a13e72dde175898b488192716b7256a05cc253c77020d/pyobjc_framework_metal-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1f3aae0f9a4192a7f4f158dbee126ab5ef63a81bf9165ec63bc50c353c8d0e6f", size = 57969, upload-time = "2025-06-14T20:51:45.051Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4f/af/b1f78770bb4b8d73d7a70140e39ca92daa2ba6b8de93d52b2ebf9db7d03e/pyobjc_framework_metal-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:d9b24d0ddb98b34a9a19755e5ca507c62fcef40ee5eae017e39be29650137f8c", size = 57994, upload-time = "2025-06-14T20:51:46.209Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/97/93/e680c0ece0e21cb20bc5d0504acd96ca6828fc766b8ed624d69230c1796d/pyobjc_framework_metal-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:de71b46062cb533be2c025cd6018fd4db9d7fd6a65bd67131d8e484c3616321a", size = 58381, upload-time = "2025-06-14T20:51:47.016Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/22/f0/b7c636729ed75d05bbb236b3b813d7629ffad5fb5951710978a478ac7713/pyobjc_framework_metal-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:b4c4dcab1db5750575a49a0a903528ea64b5bb93a9f3aaac5c810117a9c07e9c", size = 58824, upload-time = "2025-06-14T20:51:47.828Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/dc/22/8683231702db8a585c83db38cf9e76de2272673e7230de715ff3a868d0dc/pyobjc_framework_metal-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:432fefd3b27ab58c703b2f07afbc4690af815a9a8b4f8a997c4aefa8652e71d7", size = 59221, upload-time = "2025-06-14T20:51:48.691Z" },
]

[[package]]
name = "pyobjc-framework-metalfx"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/10/20/4c839a356b534c161fb97e06589f418fc78cc5a0808362bdecf4f9a61a8d/pyobjc_framework_metalfx-11.1.tar.gz", hash = "sha256:555c1b895d4ba31be43930f45e219a5d7bb0e531d148a78b6b75b677cc588fd8", size = 27002, upload-time = "2025-06-14T20:57:55.949Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/36/73/a8df8fa445a09fbc917a495a30b13fbcf224b5576c1e464d5ece9824a493/pyobjc_framework_metalfx-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:60e1dcdf133d2504d810c3a9ba5a02781c9d54c2112a9238de8e3ca4e8debf31", size = 10107, upload-time = "2025-06-14T20:51:51.783Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8e/7b/4d925bf5f1f0b0d254b3167999987ecafb251f589cd863bdbaf96eb4ad2a/pyobjc_framework_metalfx-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fdced91f6b2012c556db954de0e17f6d7985d52b4af83262f4d083bcd87aa01c", size = 10122, upload-time = "2025-06-14T20:51:52.473Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0c/b3/633bbd87f9380f8e288d02b44e70845453daf640602d15c4e167536c4b45/pyobjc_framework_metalfx-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e1b2819bd6a66ba55fb7019b45d38a803ea21b8258fa41c8e9ad7c28cfe74092", size = 10284, upload-time = "2025-06-14T20:51:53.193Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/03/87/2d9ac114e454575daf81a69da8e6170f0d357de3922b50e5ca5ca0968e30/pyobjc_framework_metalfx-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:aedfee1218b5784b010d618332a2cc088ba2ff9414eaa06e5db465eb5ef0aa43", size = 10315, upload-time = "2025-06-14T20:51:53.875Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/69/c6/98787a080b585306101e8b56f6f0bb1c579ed8f1981e9b0362a84046ec48/pyobjc_framework_metalfx-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:934cbc969182c57f5094389fe4afe6695595757d0d61f1ab663257475fdcc593", size = 10473, upload-time = "2025-06-14T20:51:54.573Z" },
]

[[package]]
name = "pyobjc-framework-metalkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/45/cb/7e01bc61625c7a6fea9c9888c9ed35aa6bbc47cda2fcd02b6525757bc2b8/pyobjc_framework_metalkit-11.1.tar.gz", hash = "sha256:8811cd81ee9583b9330df4f2499a73dcc53f3359cb92767b409acaec9e4faa1e", size = 45135, upload-time = "2025-06-14T20:57:56.601Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/87/0c/516b6d7a67a170b7d2316701d5288797a19dd283fcc2f73b7b78973e1392/pyobjc_framework_metalkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:4854cf74fccf6ce516b49bf7cf8fc7c22da9a3743914a2f4b00f336206ad47ec", size = 8730, upload-time = "2025-06-14T20:51:57.824Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/11/2a/5c55d1e57d8e90613fbce4b204b7d94a9ae7019a0928cb50cbd60bfa8191/pyobjc_framework_metalkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:62e261b7798b276fee1fee065030a5d19d173863e9c697a80d1fc9a22258ec2c", size = 8749, upload-time = "2025-06-14T20:51:58.538Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b6/e4/7b7b61d72fa235c9e364117a595c621c427217567d300da21d7417668c46/pyobjc_framework_metalkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b8a378135566e3c48838c19044e17ed2598a4050516ee1c23eee7d42439ef3c8", size = 8903, upload-time = "2025-06-14T20:51:59.392Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8a/cf/103d3233fcf2ff9ae23d5d143fde7a0d1308026ca46a35f23cffa83e6915/pyobjc_framework_metalkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ce886f3966144774d9222148eaf29fb08097d7dab5658186ded597b7c088f927", size = 8786, upload-time = "2025-06-14T20:52:01.34Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/96/63/748c15b5aa70a61c6735018d55b7a22560032f2ab060ee13349ae0aaef9c/pyobjc_framework_metalkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3e0776886fcd79fe7f0c55c718ebcdf073ac3e05d03040ab284ee09902fe1c70", size = 8948, upload-time = "2025-06-14T20:52:02.081Z" },
]

[[package]]
name = "pyobjc-framework-metalperformanceshaders"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/d0/11/5df398a158a6efe2c87ac5cae121ef2788242afe5d4302d703147b9fcd91/pyobjc_framework_metalperformanceshaders-11.1.tar.gz", hash = "sha256:8a312d090a0f51651e63d9001e6cc7c1aa04ceccf23b494cbf84b7fd3d122071", size = 302113, upload-time = "2025-06-14T20:57:57.407Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/89/df/f844516a54ef0fa1d047fe5fd94b63bc8b1218c09f7d4309b2a67a79708d/pyobjc_framework_metalperformanceshaders-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:06b2a4e446fe859e30f7efc7ccfbaefd443225a6ec53d949a113a6a4acc16c4c", size = 32888, upload-time = "2025-06-14T20:52:05.225Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b5/a2/5387ab012a20afb7252b3938a8fb5319c946a3faaa9166b79b51ab3c0bf6/pyobjc_framework_metalperformanceshaders-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:97be4bd0ded06c663205bd1cf821e148352346f147da48dba44cf7680f0ea23b", size = 32903, upload-time = "2025-06-14T20:52:06.31Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ee/8c/5f10387b638a92ffbc3ccd04bac73c68a5119672b908b6dc90d46e30fd40/pyobjc_framework_metalperformanceshaders-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:c905a3f5a34a95c1fd26bf07da505ed84b9b0a0c88a8f004914d9173f5037142", size = 33093, upload-time = "2025-06-14T20:52:07.055Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/69/69/9308e2d635f1b48c373601b26a9db9df4cdbe42ad64b72d7f147b662db65/pyobjc_framework_metalperformanceshaders-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:21ca31e4246e491df788f00978744d37db975266065f7ccbf393f027b4c6e248", size = 33012, upload-time = "2025-06-14T20:52:08.2Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2f/e6/5dfedd36c6a817afeebebe7cf748e7820df9796ca685b41b66cc09602888/pyobjc_framework_metalperformanceshaders-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c651e62ce58e75a88cfd287357fdd8d9a7f729c87248c8f43ce16025986afe6a", size = 33221, upload-time = "2025-06-14T20:52:08.976Z" },
]

[[package]]
name = "pyobjc-framework-metalperformanceshadersgraph"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metalperformanceshaders" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/32/c3/8d98661f7eecd1f1b0d80a80961069081b88efd3a82fbbed2d7e6050c0ad/pyobjc_framework_metalperformanceshadersgraph-11.1.tar.gz", hash = "sha256:d25225aab4edc6f786b29fe3d9badc4f3e2d0caeab1054cd4f224258c1b6dbe2", size = 105098, upload-time = "2025-06-14T20:57:58.273Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/0d/a1/2033cf8b0d9f059e3495a1d9a691751b242379c36dd5bcb96c8edb121c9e/pyobjc_framework_metalperformanceshadersgraph-11.1-py2.py3-none-any.whl", hash = "sha256:9b8b014e8301c2ae608a25f73bbf23c8f3f73a6f5fdbafddad509a21b84df681", size = 6461, upload-time = "2025-06-14T20:52:10.522Z" },
]

[[package]]
name = "pyobjc-framework-metrickit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/bd/48/8ae969a51a91864000e39c1de74627b12ff587b1dbad9406f7a30dfe71f8/pyobjc_framework_metrickit-11.1.tar.gz", hash = "sha256:a79d37575489916c35840e6a07edd958be578d3be7a3d621684d028d721f0b85", size = 40952, upload-time = "2025-06-14T20:57:58.996Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/55/d1/aea4655e7eaa9ab19da8fe78ab363270443059c8a542b8f8a071b4988b57/pyobjc_framework_metrickit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a034e6b982e915da881edef87d71b063e596511d52aef7a32c683571f364156e", size = 8081, upload-time = "2025-06-14T20:52:13.72Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d9/d2/1f70e7524f6aca2e7aa7a99c4024d8c7e7cdd2ae9b338d2958548ee432c0/pyobjc_framework_metrickit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:95e98e96b8f122b0141e84f13ae9e0f91d09d0803b1c093fdc7d19123f000f9e", size = 8104, upload-time = "2025-06-14T20:52:14.405Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/aa/26/d875ea9da12be79e5336e7aa9134db97eb917c968f8237235e5a70da0b72/pyobjc_framework_metrickit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:14de8dcaa107fe15546df91b1f7d51dc398169c3d1b06e02291fdb8722c6bf41", size = 8247, upload-time = "2025-06-14T20:52:15.469Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/ae/d54e66860cb083638f0dbf8e60b71931f0357c55a7eca7c25a3198c0a561/pyobjc_framework_metrickit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:75c5a62abc535387eea6a1e1612cfa5b1d59512ebfa8a3352596d481b18cc714", size = 8150, upload-time = "2025-06-14T20:52:16.933Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ef/cf/f9c1ec5241c3ffb999b6eb026df260f0336300a13324eb53e2bf44701ec0/pyobjc_framework_metrickit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:92483af233a2c31ef73dd0f7a32988a323f9560699f2f1c6c10a8a282a7b9cfd", size = 8296, upload-time = "2025-06-14T20:52:17.646Z" },
]

[[package]]
name = "pyobjc-framework-mlcompute"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/8b/e6/f064dec650fb1209f41aba0c3074416cb9b975a7cf4d05d93036e3d917f0/pyobjc_framework_mlcompute-11.1.tar.gz", hash = "sha256:f6c4c3ea6a62e4e3927abf9783c40495aa8bb9a8c89def744b0822da58c2354b", size = 89021, upload-time = "2025-06-14T20:57:59.997Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/23/cc/f47a4ac2d1a792b82206fdab58cc61b3aae15e694803ea2c81f3dfc16d9d/pyobjc_framework_mlcompute-11.1-py2.py3-none-any.whl", hash = "sha256:975150725e919f8d3d33f830898f3cd2fd19a440999faab320609487f4eae19d", size = 6778, upload-time = "2025-06-14T20:52:19.844Z" },
]

[[package]]
name = "pyobjc-framework-modelio"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a0/27/140bf75706332729de252cc4141e8c8afe16a0e9e5818b5a23155aa3473c/pyobjc_framework_modelio-11.1.tar.gz", hash = "sha256:fad0fa2c09d468ac7e49848e144f7bbce6826f2178b3120add8960a83e5bfcb7", size = 123203, upload-time = "2025-06-14T20:58:01.035Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/84/5f223b82894777388ef1aa09579d9c044044877a72075213741c97adc901/pyobjc_framework_modelio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:5d5e11389bde0852490b2a37896aaf9eb674b2a3586f2c572f9101cecb7bc576", size = 20172, upload-time = "2025-06-14T20:52:22.327Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/00/8b/7c8b93d99d2102800834011f58d6e5cbb56d24c112c2e45c4730b103e4a3/pyobjc_framework_modelio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:34fabde55d28aa8a12dd4476ad40182513cf87ee2fa928043aa6702961de302b", size = 20182, upload-time = "2025-06-14T20:52:23.063Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4d/c1/4d7830a8bd4e5b077e03e72eb8b92a336f689d5203228ecab9900d58d3c3/pyobjc_framework_modelio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:327e1f3020001fd15bfbf4d4228581a8f64bd85872fd697b7c306343c11e25a6", size = 20408, upload-time = "2025-06-14T20:52:23.813Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a1/14/a42462624d06c87034dce4cf40ded2ca6750a4d2e393607b5fb927a773b4/pyobjc_framework_modelio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:214a4078950bc7b86a1ea70504ecf292cccebe6515c70023efdddaaa6423f455", size = 20209, upload-time = "2025-06-14T20:52:24.541Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/65/db/5c24390c08fd4f895e760cc2160137248ec0c2fa8fc12cb1bdfd93fbcfa8/pyobjc_framework_modelio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1b1393ddb315c0e8bed3f6ce4e4b355869a30c81ff79bda3ca3a201c0fd06dad", size = 20440, upload-time = "2025-06-14T20:52:25.632Z" },
]

[[package]]
name = "pyobjc-framework-multipeerconnectivity"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/73/99/75bf6170e282d9e546b353b65af7859de8b1b27ddc431fc4afbf15423d01/pyobjc_framework_multipeerconnectivity-11.1.tar.gz", hash = "sha256:a3dacca5e6e2f1960dd2d1107d98399ff81ecf54a9852baa8ec8767dbfdbf54b", size = 26149, upload-time = "2025-06-14T20:58:01.793Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/fe/5c29c227f6ed81147ec6ec3e681fc680a7ffe0360f96901371435ea68570/pyobjc_framework_multipeerconnectivity-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:970031deb3dbf8da1fcb04e785d4bd2eeedae8f6677db92881df6d92b05c31d6", size = 11981, upload-time = "2025-06-14T20:52:29.406Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d3/ea/f8d928235a67feeefec80e1f679bdb0c05f94e718a9aa22b4968ad65c6d1/pyobjc_framework_multipeerconnectivity-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c92c95ea611d5272ab37fd73bc8e68c3d8fde515a75b97d8b22dafa8acbc7daf", size = 11992, upload-time = "2025-06-14T20:52:30.148Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5a/ff/e60c8681d5c916f68fc78276d9243a91efc94a0e98717b535ce0b16e9db0/pyobjc_framework_multipeerconnectivity-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:296e10d289887cc4141c660f884cced1ec4ce64a19b3e406f13f6ce453a9425f", size = 12172, upload-time = "2025-06-14T20:52:30.857Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a9/e3/2d5cea88ac0dc4ac0b2669fa43019fcdc701463c1f08e15fc5446a6dbd2a/pyobjc_framework_multipeerconnectivity-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:35c1a4a4b16df68b658b8531f97799995816a5bf49efd66805e3057b9bb9e474", size = 11980, upload-time = "2025-06-14T20:52:31.869Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c3/84/154fe3919bf085575e9bc7b617b31914f4f4238d1b3cf0a5c75a7bfff911/pyobjc_framework_multipeerconnectivity-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c28ad5c0c6d28cbc897aaebcc5f14798762aa9fec7f9110171570fef4d8d8a36", size = 12157, upload-time = "2025-06-14T20:52:32.567Z" },
]

[[package]]
name = "pyobjc-framework-naturallanguage"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a2/e9/5352fbf09c5d5360405dea49fb77e53ed55acd572a94ce9a0d05f64d2b70/pyobjc_framework_naturallanguage-11.1.tar.gz", hash = "sha256:ab1fc711713aa29c32719774fc623bf2d32168aed21883970d4896e901ff4b41", size = 46120, upload-time = "2025-06-14T20:58:02.808Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4b/f2/de86665d48737c74756b016c0f3bf93c99ca4151b48b14e2fbe7233283f8/pyobjc_framework_naturallanguage-11.1-py2.py3-none-any.whl", hash = "sha256:65a780273d2cdd12a3fa304e9c9ad822cb71facd9281f1b35a71640c53826f7c", size = 5306, upload-time = "2025-06-14T20:52:34.024Z" },
]

[[package]]
name = "pyobjc-framework-netfs"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/68/5d/d68cc59a1c1ea61f227ed58e7b185a444d560655320b53ced155076f5b78/pyobjc_framework_netfs-11.1.tar.gz", hash = "sha256:9c49f050c8171dc37e54d05dd12a63979c8b6b565c10f05092923a2250446f50", size = 15910, upload-time = "2025-06-14T20:58:03.811Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/77/cc/199b06f214f8a2db26eb47e3ab7015a306597a1bca25dcb4d14ddc65bd4a/pyobjc_framework_netfs-11.1-py2.py3-none-any.whl", hash = "sha256:f202e8e0c2e73516d3eac7a43b1c66f9911cdbb37ea32750ed197d82162c994a", size = 4143, upload-time = "2025-06-14T20:52:35.428Z" },
]

[[package]]
name = "pyobjc-framework-network"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/0a/ee/5ea93e48eca341b274027e1532bd8629fd55d609cd9c39c2c3acf26158c3/pyobjc_framework_network-11.1.tar.gz", hash = "sha256:f6df7a58a1279bbc976fd7e2efe813afbbb18427df40463e6e2ee28fba07d2df", size = 124670, upload-time = "2025-06-14T20:58:05.491Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/15/c2/3c6626fdb3616fde2c173d313d15caea22d141abcc2fbf3b615f8555abe3/pyobjc_framework_network-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8cdc9be8ec3b0ae95e5c649e4bbcdf502cffd357dacc566223be707bdd5ac271", size = 19513, upload-time = "2025-06-14T20:52:38.423Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/91/96/0824455bab6d321ccb5a38907ab8593e1c83b283ec850abee494278f1c96/pyobjc_framework_network-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:04582fef567392c2a10dcee9519356b79b17ab73ded050d14592da938d95b01a", size = 19537, upload-time = "2025-06-14T20:52:39.181Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5d/77/a088cfef5daf5841274b49fc57f5c5f70954c4a60b9a26160cb7beeb3e3a/pyobjc_framework_network-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:acf16738ab447a31a9f6167171b2a00d65a9370a8e84482d435b2b31c58eed94", size = 19600, upload-time = "2025-06-14T20:52:39.95Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/58/af/a5a22f53f0b31c584d39ddda0d3c55f41ffdbaec95a130f86fbc2e52cd0f/pyobjc_framework_network-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cafdf953aa80934d30726baa681c1af61daf2cc9fe9e3ca582f4e3796bd0d053", size = 14769, upload-time = "2025-06-14T20:52:40.678Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e6/cf/3cbbc1213caa45171fb2c8890a91302cee452283cc0be8b06aca35e2b1ad/pyobjc_framework_network-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2e45d8fdc0ad553cc35839cae5eab221fe5f7ce28758d693b8159e619ea06eac", size = 14832, upload-time = "2025-06-14T20:52:41.454Z" },
]

[[package]]
name = "pyobjc-framework-networkextension"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/71/30/d1eee738d702bbca78effdaa346a2b05359ab8a96d961b7cb44838e236ca/pyobjc_framework_networkextension-11.1.tar.gz", hash = "sha256:2b74b430ca651293e5aa90a1e7571b200d0acbf42803af87306ac8a1c70b0d4b", size = 217252, upload-time = "2025-06-14T20:58:06.311Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b6/26/526cd9f63e390e9c2153c41dc0982231b0b1ca88865deb538b77e1c3513d/pyobjc_framework_networkextension-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:853458aae8b43634461f6c44759750e2dc784c9aba561f9468ab14529b5a7fbe", size = 14114, upload-time = "2025-06-14T20:52:45.274Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/06/30/ab050541fda285e2ce6b6ba0f1f5215809bd5ec75f71de8057ff8135737a/pyobjc_framework_networkextension-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:d3d6e9810cb01c3a8f99aed5ee2d75f6f785204338b99b32e5f64370a18cc9dd", size = 14128, upload-time = "2025-06-14T20:52:46.328Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/36/3980a3ee5fe4be7c442cb4ddcf03f63406055da3f5ad58640fb573ecd77c/pyobjc_framework_networkextension-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:7dea914e7b26e28c6e4f8ffd03dd8fce612d38876043944fb0cf191774634566", size = 14275, upload-time = "2025-06-14T20:52:47.019Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/42/48/732767e8f858bd35fafce7ef846444569fb239e08d598e394c429c8bb78e/pyobjc_framework_networkextension-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4c9d6c08b8f1cf374351bcecf8bbc91e6a8999b84d52f30964f4f1e6a323943c", size = 14179, upload-time = "2025-06-14T20:52:48.126Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c8/02/9b2493f6894c873c751e097b692744ce0360248ff1b55dd64ff3716877d6/pyobjc_framework_networkextension-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6d730540d97662867f3cfd90c9a1e69a6adae0f5eb554c1b94a1b067e7ebc728", size = 14323, upload-time = "2025-06-14T20:52:48.851Z" },
]

[[package]]
name = "pyobjc-framework-notificationcenter"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a8/4a/d3529b9bd7aae2c89d258ebc234673c5435e217a5136abd8c0aba37b916b/pyobjc_framework_notificationcenter-11.1.tar.gz", hash = "sha256:0b938053f2d6b1cea9db79313639d7eb9ddd5b2a5436a346be0887e75101e717", size = 23389, upload-time = "2025-06-14T20:58:07.136Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6d/92/cd00fe5e54a191fb77611fe728a8c8a0a6edb229857d32f27806582406ca/pyobjc_framework_notificationcenter-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:65fc67374a471890245c7a1d60cf67dcf160075a9c048a5d89608a8290f33b03", size = 9880, upload-time = "2025-06-14T20:52:52.406Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/40/e4/1bc444c5ee828a042e951c264ce597207e192fb6701c380db5ba05486955/pyobjc_framework_notificationcenter-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f5ce98882e301adef07651ba495ddd57b661d4c0398afd39f4591c1b44673cca", size = 9895, upload-time = "2025-06-14T20:52:53.105Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/13/b9/b98d74bcc9e1694494b81dd1bfeb28e2f004041db4945b7451c0c6c64b1e/pyobjc_framework_notificationcenter-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e46285290d04e84c167606ccfcb9a20c2567f5a2a6a9c6e96760fc9d561c2740", size = 10090, upload-time = "2025-06-14T20:52:53.814Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4b/1e/3d6b9765f3f2719733b099cb48750366d9bbd431a1b5b0e6dd30ece7a995/pyobjc_framework_notificationcenter-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:c3e79e9c57f130099b47bde48f26fcd90ab3b52e01d989ea15b7cdb7fa5a34d8", size = 9935, upload-time = "2025-06-14T20:52:54.589Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f3/13/1a85878f14232d8b7012a5a24dbf185dec1864dc92ca53db4c62390b6ee5/pyobjc_framework_notificationcenter-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:15e49491d7f091eaa643f2fd89787becbf767dd6c609aa3d01e53132cb1d9fa1", size = 10137, upload-time = "2025-06-14T20:52:55.312Z" },
]

[[package]]
name = "pyobjc-framework-opendirectory"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/9d/02/ac56c56fdfbc24cdf87f4a624f81bbe2e371d0983529b211a18c6170e932/pyobjc_framework_opendirectory-11.1.tar.gz", hash = "sha256:319ac3424ed0350be458b78148914468a8fc13a069d62e7869e3079108e4f118", size = 188880, upload-time = "2025-06-14T20:58:08.003Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/06/56/f0f5b7222d5030192c44010ab7260681e349efea2f1b1b9f116ba1951d6d/pyobjc_framework_opendirectory-11.1-py2.py3-none-any.whl", hash = "sha256:bb4219b0d98dff4a952c50a79b1855ce74e1defd0d241f3013def5b09256fd7b", size = 11829, upload-time = "2025-06-14T20:52:56.715Z" },
]

[[package]]
name = "pyobjc-framework-osakit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/56/22/f9cdfb5de255b335f99e61a3284be7cb1552a43ed1dfe7c22cc868c23819/pyobjc_framework_osakit-11.1.tar.gz", hash = "sha256:920987da78b67578367c315d208f87e8fab01dd35825d72242909f29fb43c820", size = 22290, upload-time = "2025-06-14T20:58:09.103Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/14/65/c6531ce0792d5035d87f054b0ccf22e453328fda2e68e11a7f70486da23a/pyobjc_framework_osakit-11.1-py2.py3-none-any.whl", hash = "sha256:1b0c0cc537ffb8a8365ef9a8b46f717a7cc2906414b6a3983777a6c0e4d53d5a", size = 4143, upload-time = "2025-06-14T20:52:57.555Z" },
]

[[package]]
name = "pyobjc-framework-oslog"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/79/93/3feb7f6150b50165524750a424f5434448392123420cb4673db766c3f54a/pyobjc_framework_oslog-11.1.tar.gz", hash = "sha256:b2af409617e6b68fa1f1467c5a5679ebf59afd0cdc4b4528e1616059959a7979", size = 24689, upload-time = "2025-06-14T20:58:09.739Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/40/da/fd3bd62899cd679743056aa2c28bc821c2688682a17ddde1a08d6d9d67fc/pyobjc_framework_oslog-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7ae29c31ce51c476d3a37ca303465dd8bdfa98df2f6f951cf14c497e984a1ba9", size = 7799, upload-time = "2025-06-14T20:52:59.935Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9d/a9/d26bb3ec7ab2a3ef843c1697b6084dbd4a4a98d90ff8e29f4c227ade425e/pyobjc_framework_oslog-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7174ca2cdc073e555d5f5aea3baa7410c61a83a3741eaec23e8581340037680e", size = 7811, upload-time = "2025-06-14T20:53:00.621Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/44/60/2f57ee052e9df2700b21032774146ae622af0a88a8dff97158dc5850a0ec/pyobjc_framework_oslog-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f03789f8d5638e1075652b331b8ebf98c03dfa809c57545f0313583a7688bb86", size = 7995, upload-time = "2025-06-14T20:53:01.316Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2f/f1/13fe8d1cebe29953e8754d9118399805b266e17ef885f628f62f2d2deb9b/pyobjc_framework_oslog-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a302272aa40d1655be635e0f0dd0ca71b5fce562dfcb88a87165a170a648b2fd", size = 7847, upload-time = "2025-06-14T20:53:02.032Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/37/82/a5a2fb3333c3f55ba696baee67668e44380b9838dd91b64a038ed57cee41/pyobjc_framework_oslog-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:cade8869e185a29fb88fc48e2e5c984548433f669c1a40ec7f5640994fa36603", size = 8034, upload-time = "2025-06-14T20:53:02.72Z" },
]

[[package]]
name = "pyobjc-framework-passkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/5c/05/063db500e7df70e39cbb5518a5a03c2acc06a1ca90b057061daea00129f3/pyobjc_framework_passkit-11.1.tar.gz", hash = "sha256:d2408b58960fca66607b483353c1ffbd751ef0bef394a1853ec414a34029566f", size = 144859, upload-time = "2025-06-14T20:58:10.761Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9d/ba/9e52213e0c0100079e4ef397cf4fd5ba8939fa4de19339755d1a373407a8/pyobjc_framework_passkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:779eaea4e1931cfda4c8701e1111307b14bf9067b359a319fc992b6848a86932", size = 13959, upload-time = "2025-06-14T20:53:05.694Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d1/4f/e29dc665382e22cd6b4ebb1c5707a1b2059018a6462c81a7c344a9c40dba/pyobjc_framework_passkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a6306dda724ca812dca70154d40f32ec9bbdaff765a12f3cc45391723efe147e", size = 13971, upload-time = "2025-06-14T20:53:06.413Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f4/ec/ef03f62924b288302e41373c4c292cadf4c393519828a9986d8573b72bcc/pyobjc_framework_passkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d7948d5b3369b60808a85dcadffdebb0a44e8d2c4716edc10b78cb76fa762070", size = 14130, upload-time = "2025-06-14T20:53:07.169Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/92/cb/4ecaf64825de3589cbf5119cf6bfabe7b466faff58357800255c2ecf41e1/pyobjc_framework_passkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bfff2a63850afe702ba25f661360393389ffb58e127d47488c414caa9e676aa7", size = 14010, upload-time = "2025-06-14T20:53:08.254Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ce/72/125088bd20a8f771cc1749c6be786241839c6bdb6a581cf025663f55fa1f/pyobjc_framework_passkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f6b7f3cd7c6855af1b6fc4036ae2f10779a312182107c94d36ef63c2dd4a6f87", size = 14180, upload-time = "2025-06-14T20:53:08.972Z" },
]

[[package]]
name = "pyobjc-framework-pencilkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/75/d0/bbbe9dadcfc37e33a63d43b381a8d9a64eca27559df38efb74d524fa6260/pyobjc_framework_pencilkit-11.1.tar.gz", hash = "sha256:9c173e0fe70179feadc3558de113a8baad61b584fe70789b263af202bfa4c6be", size = 22570, upload-time = "2025-06-14T20:58:11.538Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a3/f6/59ffc3f26ea9cfda4d40409f9afc2a38e5c0c6a68a3a8c9202e8b98b03b1/pyobjc_framework_pencilkit-11.1-py2.py3-none-any.whl", hash = "sha256:b7824907bbcf28812f588dda730e78f662313baf40befd485c6f2fcb49018019", size = 4026, upload-time = "2025-06-14T20:53:10.449Z" },
]

[[package]]
name = "pyobjc-framework-phase"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/c6/d2/e9384b5b3fbcc79e8176cb39fcdd48b77f60cd1cb64f9ee4353762b037dc/pyobjc_framework_phase-11.1.tar.gz", hash = "sha256:a940d81ac5c393ae3da94144cf40af33932e0a9731244e2cfd5c9c8eb851e3fc", size = 58986, upload-time = "2025-06-14T20:58:12.196Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f5/9e/55782f02b3bfb58f030b062176e8b0dba5f8fbd6e50d27a687f559c4179d/pyobjc_framework_phase-11.1-py2.py3-none-any.whl", hash = "sha256:cfa61f9c6c004161913946501538258aed48c448b886adbf9ed035957d93fa15", size = 6822, upload-time = "2025-06-14T20:53:11.618Z" },
]

[[package]]
name = "pyobjc-framework-photos"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/b0/576652ecd05c26026ab4e75e0d81466edd570d060ce7df3d6bd812eb90d0/pyobjc_framework_photos-11.1.tar.gz", hash = "sha256:c8c3b25b14a2305047f72c7c081ff3655b3d051f7ed531476c03246798f8156d", size = 92569, upload-time = "2025-06-14T20:58:12.939Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fa/24/2400e6b738d3ed622c61a7cc6604eec769f398071a1eb6a16dfdf3a9ceea/pyobjc_framework_photos-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8dbfffd29cfa63a8396ede0030785c15a5bc36065d3dd98fc6176a59e7abb3d3", size = 12224, upload-time = "2025-06-14T20:53:14.793Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/70/60/cc575ee4287b250a42406e9b335f3293840996a840152cf93d1ce73790c5/pyobjc_framework_photos-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:541d8fafdb2f111f2f298e1aa0542f2d5871ce1dd481c3e9be4ed33916b38c3a", size = 12241, upload-time = "2025-06-14T20:53:15.469Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8c/3b/d9c4c5b156e7805495a8864dd06a3439c3b4267e5887d9094ac45a4ca907/pyobjc_framework_photos-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:7cded282eaebd77645a4262f6fb63379c7a226d20f8f1763910b19927709aea2", size = 12426, upload-time = "2025-06-14T20:53:16.207Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/28/86/06d9e61aa5c6114cca5ae77e3c037f371943e9110aab4ce6d31d19ffb669/pyobjc_framework_photos-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3a759ebcf46493cd09e5c89c0a09096ad83ae837d9236e437571bb22ca6eab3f", size = 12290, upload-time = "2025-06-14T20:53:16.897Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/69/07/849ca5aefc646b92ea399073f90628215198701a59c1b62b7bf3e27bbbdf/pyobjc_framework_photos-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:72e0ed9bc5f1890f882df55333797da95c0ed1c1d7a0fe7d869a8d4ee4e1bdfd", size = 12470, upload-time = "2025-06-14T20:53:17.592Z" },
]

[[package]]
name = "pyobjc-framework-photosui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/20/bb/e6de720efde2e9718677c95c6ae3f97047be437cda7a0f050cd1d6d2a434/pyobjc_framework_photosui-11.1.tar.gz", hash = "sha256:1c7ffab4860ce3e2b50feeed4f1d84488a9e38546db0bec09484d8d141c650df", size = 48443, upload-time = "2025-06-14T20:58:13.626Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f8/c1/a5c84c1695e7a066743d63d10b219d94f3c07d706871682e42f7db389f5c/pyobjc_framework_photosui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b2f278f569dfd596a32468351411518a651d12cb91e60620094e852c525a5f10", size = 11682, upload-time = "2025-06-14T20:53:21.162Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/33/10/506af430a9e7d356302b6bbee6672e03a4dfbc9a2f3a90fa79607d06387d/pyobjc_framework_photosui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6f0fa9c9e363c0db54957dfe4e26214379f2698caaba1e4ff4c9e3eba5e690d9", size = 11697, upload-time = "2025-06-14T20:53:21.855Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9f/f8/ada0d54136f14b071e784e7f86e0a1e2190e2e898a7f4172b53e1fec5f7c/pyobjc_framework_photosui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:91aff7caae16a7a7f25e35692aa92b796155510b8a0575668e75f351fbf63a68", size = 11894, upload-time = "2025-06-14T20:53:22.536Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1b/7d/b55a787f90e29f36b776cf87b9515a53014449d9cddd109b9e81c9e9d7eb/pyobjc_framework_photosui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e607242e09fb7d4bcad2f3eb2e88529d8f2ff7cf7341cd2c6c5b3f4d6744218e", size = 11670, upload-time = "2025-06-14T20:53:23.22Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/be/3e98e69e513b3948080ede2a13b0f73f081db50c716519fcee4a932de0b6/pyobjc_framework_photosui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f11f6043c83b2c65ecad69c48844fff6368127af3956ec8df9726bbd1e5da17e", size = 11891, upload-time = "2025-06-14T20:53:23.901Z" },
]

[[package]]
name = "pyobjc-framework-preferencepanes"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/34/ac/9324602daf9916308ebf1935b8a4b91c93b9ae993dcd0da731c0619c2836/pyobjc_framework_preferencepanes-11.1.tar.gz", hash = "sha256:6e4a55195ec9fc921e0eaad6b3038d0ab91f0bb2f39206aa6fccd24b14a0f1d8", size = 26212, upload-time = "2025-06-14T20:58:14.361Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a1/51/75c7e32272241f706ce8168e04a32be02c4b0c244358330f730fc85695c3/pyobjc_framework_preferencepanes-11.1-py2.py3-none-any.whl", hash = "sha256:6ee5f5a7eb294e03ea3bac522ac4b69e6dc83ceceff627a0a2d289afe1e01ad9", size = 4786, upload-time = "2025-06-14T20:53:25.603Z" },
]

[[package]]
name = "pyobjc-framework-pushkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/9f/f0/92d0eb26bf8af8ebf6b5b88df77e70b807de11f01af0162e0a429fcfb892/pyobjc_framework_pushkit-11.1.tar.gz", hash = "sha256:540769a4aadc3c9f08beca8496fe305372501eb28fdbca078db904a07b8e10f4", size = 21362, upload-time = "2025-06-14T20:58:15.642Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/31/65/260014c5d13c54bd359221b0a890cbffdb99eecff3703f253cf648e45036/pyobjc_framework_pushkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:21993b7e9127b05575a954faa68e85301c6a4c04e34e38aff9050f67a05c562a", size = 8174, upload-time = "2025-06-14T20:53:28.805Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/b2/08514fa6be83a359bb6d72f9009f17f16f7efc0fe802029d1f6f0c4fc5c9/pyobjc_framework_pushkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:bac3ee77dfbe936998f207c1579e346993485bab8849db537ed250261cf12ab3", size = 8190, upload-time = "2025-06-14T20:53:29.651Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/46/d0/cbe99c9bf3b9fb2679c08f4051aaa44dcfbfa9e762f0ef4c7fc5ad2e147e/pyobjc_framework_pushkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:68c4f44354eab84cb54d43310fa65ca3a5ba68299c868378764cc50803cf2adc", size = 8314, upload-time = "2025-06-14T20:53:31.178Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/87/ff/7b0747471b837580dc01709438a5a0949ce909957d2857408bd81bf22155/pyobjc_framework_pushkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cfec36cdca24654be0465282eb31b7ff3674ea4b7f3ce696b07edbe33b000aa5", size = 8240, upload-time = "2025-06-14T20:53:31.852Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/86/96/422875f53390579dd51d1cdc696290c5693d293e9c4cb0f6d4e7a0905f88/pyobjc_framework_pushkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:80d5d8240b71631d81cfa96f398fae1d137be98f224739e50edaf9e5afc21a9d", size = 8368, upload-time = "2025-06-14T20:53:32.53Z" },
]

[[package]]
name = "pyobjc-framework-quartz"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/c7/ac/6308fec6c9ffeda9942fef72724f4094c6df4933560f512e63eac37ebd30/pyobjc_framework_quartz-11.1.tar.gz", hash = "sha256:a57f35ccfc22ad48c87c5932818e583777ff7276605fef6afad0ac0741169f75", size = 3953275, upload-time = "2025-06-14T20:58:17.924Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9b/37/ee6e0bdd31b3b277fec00e5ee84d30eb1b5b8b0e025095e24ddc561697d0/pyobjc_framework_quartz-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9ac806067541917d6119b98d90390a6944e7d9bd737f5c0a79884202327c9204", size = 216410, upload-time = "2025-06-14T20:53:36.346Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/bd/27/4f4fc0e6a0652318c2844608dd7c41e49ba6006ee5fb60c7ae417c338357/pyobjc_framework_quartz-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:43a1138280571bbf44df27a7eef519184b5c4183a588598ebaaeb887b9e73e76", size = 216816, upload-time = "2025-06-14T20:53:37.358Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b8/8a/1d15e42496bef31246f7401aad1ebf0f9e11566ce0de41c18431715aafbc/pyobjc_framework_quartz-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b23d81c30c564adf6336e00b357f355b35aad10075dd7e837cfd52a9912863e5", size = 221941, upload-time = "2025-06-14T20:53:38.34Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/32/a8/a3f84d06e567efc12c104799c7fd015f9bea272a75f799eda8b79e8163c6/pyobjc_framework_quartz-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:07cbda78b4a8fcf3a2d96e047a2ff01f44e3e1820f46f0f4b3b6d77ff6ece07c", size = 221312, upload-time = "2025-06-14T20:53:39.435Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/76/ef/8c08d4f255bb3efe8806609d1f0b1ddd29684ab0f9ffb5e26d3ad7957b29/pyobjc_framework_quartz-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:39d02a3df4b5e3eee1e0da0fb150259476910d2a9aa638ab94153c24317a9561", size = 226353, upload-time = "2025-06-14T20:53:40.655Z" },
]

[[package]]
name = "pyobjc-framework-quicklookthumbnailing"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/aa/98/6e87f360c2dfc870ae7870b8a25fdea8ddf1d62092c755686cebe7ec1a07/pyobjc_framework_quicklookthumbnailing-11.1.tar.gz", hash = "sha256:1614dc108c1d45bbf899ea84b8691288a5b1d25f2d6f0c57dfffa962b7a478c3", size = 16527, upload-time = "2025-06-14T20:58:20.811Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/65/4a/ddc35bdcd44278f22df2154a52025915dba6c80d94e458d92e9e7430d1e4/pyobjc_framework_quicklookthumbnailing-11.1-py2.py3-none-any.whl", hash = "sha256:4d1863c6c83c2a199c1dbe704b4f8b71287168f4090ed218d37dc59277f0d9c9", size = 4219, upload-time = "2025-06-14T20:53:43.198Z" },
]

[[package]]
name = "pyobjc-framework-replaykit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/c8/4f/014e95f0fd6842d7fcc3d443feb6ee65ac69d06c66ffa9327fc33ceb7c27/pyobjc_framework_replaykit-11.1.tar.gz", hash = "sha256:6919baa123a6d8aad769769fcff87369e13ee7bae11b955a8185a406a651061b", size = 26132, upload-time = "2025-06-14T20:58:21.853Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/73/846cebb36fc279df18f10dc3a27cba8fe2e47e95350a3651147e4d454719/pyobjc_framework_replaykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:22c6d09be9a6e758426d723a6c3658ad6bbb66f97ba9a1909bfcf29a91d99921", size = 10087, upload-time = "2025-06-14T20:53:46.242Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/bf/2e/996764cd045b6c9e033167e573c9fe67c4e867eb6ab49c2d4fde005cd4a7/pyobjc_framework_replaykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7742ee18c8c9b61f5668698a05b88d25d34461fcdd95a8f669ecdfd8db8c4d42", size = 10108, upload-time = "2025-06-14T20:53:47.293Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d6/f9/1013a88f655b9eaf6fc81a5da48403724435cf2f87c147038dfa733e6213/pyobjc_framework_replaykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b503fabc33ee02117fd82c78db18cba3f0be90dea652f5553101a45185100402", size = 10298, upload-time = "2025-06-14T20:53:47.992Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fc/df/62a735c034bdbd0670f93636725b898a762fd23532a3841ae491bc8d16bd/pyobjc_framework_replaykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:da84e48ba5d529ae72b975f0d81c5bd5427983c2b05d3d2c7fd54a6cbdf0d0f9", size = 10170, upload-time = "2025-06-14T20:53:48.682Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/56/00/d582fd058e580e5f803ee57fa8513b7df0c6d2abca876e04a4bc682b7143/pyobjc_framework_replaykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2bf2180feae500fdd6f14360200fda0b6650a4ec39fe5d84a5dde9e8cdd307b6", size = 10347, upload-time = "2025-06-14T20:53:49.383Z" },
]

[[package]]
name = "pyobjc-framework-safariservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/1a/fc/c47d2abf3c1de6db21d685cace76a0931d594aa369e3d090260295273f6e/pyobjc_framework_safariservices-11.1.tar.gz", hash = "sha256:39a17df1a8e1c339457f3acbff0dc0eae4681d158f9d783a11995cf484aa9cd0", size = 34905, upload-time = "2025-06-14T20:58:22.492Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d7/13/9636e9d3dc362daaaa025b2aa4e28606a1e197dfc6506d3a246be8315f8a/pyobjc_framework_safariservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c92eb9e35f98368ea1bfaa8cdd41138ca8b004ea5a85833390a44e5626ca5061", size = 7275, upload-time = "2025-06-14T20:53:53.075Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/de/cd/9ed0083373be3bf6da2450a6800b54965fea95b2452473ee0e36ddc72573/pyobjc_framework_safariservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8b4d4169dd21e69246d90a42f872b7148064b63de6bbbf6bc6ddabe33f143843", size = 7290, upload-time = "2025-06-14T20:53:53.816Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/42/ed/3eaec77c81395410441466f66c8920664ba72f62099306f0e9b878b0b203/pyobjc_framework_safariservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:8a4371d64052a3ffe9993a89c45f9731f86e7b6c21fd1d968815fd7930ff501a", size = 7293, upload-time = "2025-06-14T20:53:54.508Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d2/5f/5bbdf64ec7ff2c1d90e0b7b7186a55981632c16ce757b3187e87d6707c7e/pyobjc_framework_safariservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:abdbe0d8a79caa994a1d2be8ea4e5a1e4c80f7d8e1f0750f9c365129d1f1a968", size = 7312, upload-time = "2025-06-14T20:53:55.193Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fd/2a/dd6d53915c83c1e68bd8cfdec5cf71c4b3c6e1b7c737353f109b2dde5426/pyobjc_framework_safariservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8a6ec417d35a0600629eba97c0ab2f2d09fae171e8bca3d3d6aa1c7ff272c4d7", size = 7318, upload-time = "2025-06-14T20:53:55.875Z" },
]

[[package]]
name = "pyobjc-framework-safetykit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/28/cc/f6aa5d6f45179bd084416511be4e5b0dd0752cb76daa93869e6edb806096/pyobjc_framework_safetykit-11.1.tar.gz", hash = "sha256:c6b44e0cf69e27584ac3ef3d8b771d19a7c2ccd9c6de4138d091358e036322d4", size = 21240, upload-time = "2025-06-14T20:58:23.132Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9c/8f/6f4c833e31526a81faef9bf19695b332ba8d2fa53d92640abd6fb3ac1d78/pyobjc_framework_safetykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b76fccdb970d3d751a540c47712e9110afac9abea952cb9b7bc0d5867db896e3", size = 8523, upload-time = "2025-06-14T20:53:59.443Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/85/3d/782e1738f2eb4b276baabd85a8b263bf75b2c4e990fd5950eeadfb59ebeb/pyobjc_framework_safetykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8130de57f701dbccb1d84c76ec007fe04992da58cbf0eb906324393eeac3d08d", size = 8541, upload-time = "2025-06-14T20:54:00.461Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/be/2c/411d525a2110777dd22888e46a48dcff2ae15ff08ab2f739eab44ee740cb/pyobjc_framework_safetykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:cd8091c902037eac4a403d8462424afd711f43206af3548a34bebe1f59d2c340", size = 8701, upload-time = "2025-06-14T20:54:01.156Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ca/df/f04b5caa76b2e4c5115c55937b50c341963c35ded6931cb1a3bc0e686d0b/pyobjc_framework_safetykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:761304365978d650015fe05fb624ba13ea4af6c6a76ef8e344673f5b0fed2e92", size = 8581, upload-time = "2025-06-14T20:54:01.838Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a5/66/e0bd5ac4956e4f6d77815c85355764e43934a31c8fdd10e33b4ff217cb99/pyobjc_framework_safetykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:24d5ce9dfb80abb634a95ceda3da0f0cdb52c765db0f47de953a4f66b918c957", size = 8746, upload-time = "2025-06-14T20:54:02.534Z" },
]

[[package]]
name = "pyobjc-framework-scenekit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/64/cf/2d89777120d2812e7ee53c703bf6fc8968606c29ddc1351bc63f0a2a5692/pyobjc_framework_scenekit-11.1.tar.gz", hash = "sha256:82941f1e5040114d6e2c9fd35507244e102ef561c637686091b71a7ad0f31306", size = 214118, upload-time = "2025-06-14T20:58:24.003Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e0/f9/bdcd8a4bc6c387ef07f3e2190cea6a03d4f7ed761784f492b01323e8d900/pyobjc_framework_scenekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c803d95b30c4ce49f46ff7174806f5eb84e4c3a152f8f580c5da0313c5c67041", size = 33558, upload-time = "2025-06-14T20:54:05.59Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ce/5e/9bb308fd68b56a8cf9ea5213e6c988232ce6ae4e6ccd4cf53b38f0018deb/pyobjc_framework_scenekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2f347d5ae42af8acddb86a45f965046bb91f8d83d33851390954439961e2a7b7", size = 33577, upload-time = "2025-06-14T20:54:06.69Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e0/96/c960c553de8e70f0bff275e19295b6254127f3f6d1da4e5dd80fd7037d49/pyobjc_framework_scenekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ea2f02eea982872994d7c366f6a51060a90cc17b994c017f85c094e2bc346847", size = 33912, upload-time = "2025-06-14T20:54:07.456Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/04/29/c342990cc245a3bdbb9d55807ce8009575acb705dbce24164001850ec41e/pyobjc_framework_scenekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2be143172b43c2cf4a2b3fad9e15ffb5d29df677d3678160cd125b94a30caaca", size = 34061, upload-time = "2025-06-14T20:54:08.571Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/25/aa/eff356d201d32b1f7e2a2e8c6629899cb31bcc33933816055ce1b90df31a/pyobjc_framework_scenekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3f62f2b8f26375ecfec71f7fdb23f2739cf93d213968c6ffac6a8525516ffc6e", size = 34365, upload-time = "2025-06-14T20:54:09.329Z" },
]

[[package]]
name = "pyobjc-framework-screencapturekit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/32/a5/9bd1f1ad1773a1304ccde934ff39e0f0a0b0034441bf89166aea649606de/pyobjc_framework_screencapturekit-11.1.tar.gz", hash = "sha256:11443781a30ed446f2d892c9e6642ca4897eb45f1a1411136ca584997fa739e0", size = 53548, upload-time = "2025-06-14T20:58:24.837Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/98/37/840f306dcf01dd2bd092ae8dcf371a3bad3a0f88f0780d0840f899a8c047/pyobjc_framework_screencapturekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:641fa7834f54558859209e174c83551d5fa239ca6943ace52665f7d45e562ff2", size = 11308, upload-time = "2025-06-14T20:54:12.382Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1b/9e/de4c2e3ae834c2f60c9e78d95e1f2488b679b4cf74fa5bfba7f065fb827b/pyobjc_framework_screencapturekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1119d6258d6c668564ab39154cfc745fd2bb8b3beeaa4f9b2a8a4c93926678c0", size = 11324, upload-time = "2025-06-14T20:54:13.104Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4c/49/fa1680b8453fb5c4bbe92b2bfef145fd90b3cd9c2ee24c1eb786b7655cd3/pyobjc_framework_screencapturekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f93f8198741bd904d423a7b1ef941445246bdf6cb119597d981e61a13cc479a4", size = 11517, upload-time = "2025-06-14T20:54:13.829Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/12/cd/035192d486f4323d0d891b50fd2229a58e80fd341e19fa7ae9d71c38c8e2/pyobjc_framework_screencapturekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9e135b414d3829fcf7fd8a66c94e8b51135fb9f630c10488fb9d78f27f622906", size = 11396, upload-time = "2025-06-14T20:54:14.881Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a3/4a/e2752b1d91ce420ccd58a24e5e819230007fa50e97719a78857a76f8ab6d/pyobjc_framework_screencapturekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9972db69064b69e78fbc6a00f1de2d8eaa225b990b23687970328b061e60e26d", size = 11578, upload-time = "2025-06-14T20:54:15.562Z" },
]

[[package]]
name = "pyobjc-framework-screensaver"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/7c/f6/f2d48583b29fc67b64aa1f415fd51faf003d045cdb1f3acab039b9a3f59f/pyobjc_framework_screensaver-11.1.tar.gz", hash = "sha256:d5fbc9dc076cc574ead183d521840b56be0c160415e43cb8e01cfddd6d6372c2", size = 24302, upload-time = "2025-06-14T20:58:25.52Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/76/f9/4ae982c7a1387b64954130b72187e140329b73c647acb4d6b6eb3c033d8d/pyobjc_framework_screensaver-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f2d22293cf9d715e4692267a1678096afd6793c0519d9417cf77c8a6c706a543", size = 8402, upload-time = "2025-06-14T20:54:19.044Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/dc/ff/c2e83551474d3c401181ce1d859ebd0e0b1986ab8ee932d647debebbe7eb/pyobjc_framework_screensaver-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:46d65c1e14d35f287e7be351e2f98daf9489e31e7ca0d306e6102904ce6c40fb", size = 8419, upload-time = "2025-06-14T20:54:19.741Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7a/b7/e633cd8e07bcfcd675155c7fd00f82cab0d09ca3edee0f568bcfc0ae8ea4/pyobjc_framework_screensaver-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2c01a9646bc118445cbb117e7016bd1df9fe93a65db991ab5496d59b1a7bc66d", size = 8423, upload-time = "2025-06-14T20:54:20.447Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/65/55/ac2b76a86646b6f86163d1e06c2ca36f4b0fb168ae889ab3af657b724817/pyobjc_framework_screensaver-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e32c83e1d9e5044d482916ac42257a87d1f1068f3f6bccaa04edda40fb9f9ad1", size = 8457, upload-time = "2025-06-14T20:54:21.131Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d5/e7/494e6aa650c071abd3b44a0168123a174636a1fc9d198f0db80d642703cc/pyobjc_framework_screensaver-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:7852c2281148cb99c87c4c25b83dca7fdd11e6eed04deadcf2201ed5a2079e5f", size = 8462, upload-time = "2025-06-14T20:54:21.949Z" },
]

[[package]]
name = "pyobjc-framework-screentime"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/82/33/ebed70a1de134de936bb9a12d5c76f24e1e335ff4964f9bb0af9b09607f1/pyobjc_framework_screentime-11.1.tar.gz", hash = "sha256:9bb8269456bbb674e1421182efe49f9168ceefd4e7c497047c7bf63e2f510a34", size = 14875, upload-time = "2025-06-14T20:58:26.179Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ea/20/783eccea7206ceeda42a09a4614e3da92889e4c54abe9dec2e5e53576e1a/pyobjc_framework_screentime-11.1-py2.py3-none-any.whl", hash = "sha256:50a4e4ab33d6643a52616e990aa1c697d5e3e8f9f9bdab8d631e6d42d8287b4f", size = 3949, upload-time = "2025-06-14T20:54:26.916Z" },
]

[[package]]
name = "pyobjc-framework-scriptingbridge"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/8e/c1/5b1dd01ff173df4c6676f97405113458918819cb2064c1735b61948e8800/pyobjc_framework_scriptingbridge-11.1.tar.gz", hash = "sha256:604445c759210a35d86d3e0dfcde0aac8e5e3e9d9e35759e0723952138843699", size = 23155, upload-time = "2025-06-14T20:58:26.812Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c1/64/31849063e3e81b4c312ce838dc98f0409c09eb33bc79dbb5261cb994a4c4/pyobjc_framework_scriptingbridge-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:226ba12d9cbd504411b702323b0507dd1690e81b4ce657c5f0d8b998c46cf374", size = 8323, upload-time = "2025-06-14T20:54:30.105Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d8/19/3003d4a137ce84fa8cb42a9c84f8c04e83c89749ab9cf93bc755016434b7/pyobjc_framework_scriptingbridge-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c2ba0ad3d3e4e3c6a43fe3e84ab02c5c4e74000bb6f130ae47bf82a3dcd4af98", size = 8337, upload-time = "2025-06-14T20:54:30.81Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e3/1c/0b90b4bcef7ea8fb80cb5f6fa0b73be075f2dffa2ba03580b37592dc8dad/pyobjc_framework_scriptingbridge-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:57f5401826e3a008d9cfb7c164187859cadc1b1f96194dc0a7c596f502548c26", size = 8485, upload-time = "2025-06-14T20:54:31.518Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/bc/9d/22238e06780630ae3ec26d6af17df87d649fca0d9879caeaaf4f36b147c1/pyobjc_framework_scriptingbridge-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a84d0a8ff4fa1f0016f5d797ad93e22e437212a2fc8e6417a3b8d68f89229680", size = 8346, upload-time = "2025-06-14T20:54:32.235Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/e1/fc755423ffc3b28a4c2905c607e55cbed471edc025ec5c0849de4bea1230/pyobjc_framework_scriptingbridge-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5381e9be1299e1134489e4d46662c649613214265b3b691264cfba0b083929f5", size = 8499, upload-time = "2025-06-14T20:54:32.918Z" },
]

[[package]]
name = "pyobjc-framework-searchkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/6e/20/61b73fddae0d1a94f5defb0cd4b4f391ec03bfcce7ebe830cb827d5e208a/pyobjc_framework_searchkit-11.1.tar.gz", hash = "sha256:13a194eefcf1359ce9972cd92f2aadddf103f3efb1b18fd578ba5367dff3c10c", size = 30918, upload-time = "2025-06-14T20:58:27.447Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2b/ed/a118d275a9132c8f5adcd353e4d9e844777068e33d51b195f46671161a7f/pyobjc_framework_searchkit-11.1-py2.py3-none-any.whl", hash = "sha256:9c9d6ca71cef637ccc3627225fb924a460b3d0618ed79bb0b3c12fcbe9270323", size = 3714, upload-time = "2025-06-14T20:54:34.329Z" },
]

[[package]]
name = "pyobjc-framework-security"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/ee/6f/ba50ed2d9c1192c67590a7cfefa44fc5f85c776d1e25beb224dec32081f6/pyobjc_framework_security-11.1.tar.gz", hash = "sha256:dabcee6987c6bae575e2d1ef0fcbe437678c4f49f1c25a4b131a5e960f31a2da", size = 302291, upload-time = "2025-06-14T20:58:28.506Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/35/16/7fc52ab1364ada5885bf9b4c9ea9da3ad892b847c9b86aa59e086b16fc11/pyobjc_framework_security-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2eb4ba6d8b221b9ad5d010e026247e8aa26ee43dcaf327e848340ed227d22d7e", size = 41222, upload-time = "2025-06-14T20:54:37.032Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3f/d8/cb20b4c4d15b2bdc7e39481159e50a933ddb87e4702d35060c254b316055/pyobjc_framework_security-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:158da3b2474e2567fd269531c4ee9f35b8ba4f1eccbd1fb4a37c85a18bf1243c", size = 41221, upload-time = "2025-06-14T20:54:37.803Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/cb/3c/d13d6870f5d66f5379565887b332f86f16d666dc50a1944d7e3a1462e76c/pyobjc_framework_security-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:141cc3ee08627ae0698264efc3dbbaf28d2255e0fe690e336eb8f0f387c4af01", size = 42099, upload-time = "2025-06-14T20:54:38.627Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f0/3d/2f61d4566e80f203d0e05ddd788037dc06a94d200edac25d2747fd79b5aa/pyobjc_framework_security-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:858a18303711eb69d18d1a64cf8bb2202f64a3bd1c82203c511990dbd8326514", size = 41288, upload-time = "2025-06-14T20:54:39.432Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/15/44/99ef33a5319ed2cb6c0a51ed36214adf21ccb37cce970b1acc8bfe57ce23/pyobjc_framework_security-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4db1ebf6395cd370139cb35ff172505fc449c7fdf5d3a28f2ada8a30ef132cd0", size = 42849, upload-time = "2025-06-14T20:54:40.174Z" },
]

[[package]]
name = "pyobjc-framework-securityfoundation"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/5c/d4/19591dd0938a45b6d8711ef9ae5375b87c37a55b45d79c52d6f83a8d991f/pyobjc_framework_securityfoundation-11.1.tar.gz", hash = "sha256:b3c4cf70735a93e9df40f3a14478143959c415778f27be8c0dc9ae0c5b696b92", size = 13270, upload-time = "2025-06-14T20:58:29.304Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6c/ab/23db6b1c09810d6bcc4eab96e62487fb4284b57e447eabe6c001cb41e36d/pyobjc_framework_securityfoundation-11.1-py2.py3-none-any.whl", hash = "sha256:25f2cf10f80c122f462e9d4d43efe9fd697299c194e0c357e76650e234e6d286", size = 3772, upload-time = "2025-06-14T20:54:41.732Z" },
]

[[package]]
name = "pyobjc-framework-securityinterface"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/a1/be/c846651c3e7f38a637c40ae1bcda9f14237c2395637c3a188df4f733c727/pyobjc_framework_securityinterface-11.1.tar.gz", hash = "sha256:e7aa6373e525f3ae05d71276e821a6348c53fec9f812b90eec1dbadfcb507bc9", size = 37648, upload-time = "2025-06-14T20:58:29.932Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6f/ab/48b8027a24f3f8924f5be5f97217961b4ed23e6be49b3bd94ee8a0d56a1e/pyobjc_framework_securityinterface-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:26056441b325029da06a7c7b8dd1a0c9a4ad7d980596c1b04d132a502b4cacc0", size = 10837, upload-time = "2025-06-14T20:54:44.052Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/31/2e/de226a3caa47b4a800c8e6289b9fe30c71f10985dbc37379d5bd0781b470/pyobjc_framework_securityinterface-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:708dd1d65309f3d4043ecaf152591c240601a5d3da7ae7a500f511c54317537b", size = 10851, upload-time = "2025-06-14T20:54:45.254Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2a/9f/2d0c41ded78f9dc1e58d63b9d7ed55666b0d0d6ec78ce8938c7c4accdf59/pyobjc_framework_securityinterface-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e9ebfb32177eb06f5c894be97c6af3802f09b9890fce8e0956cc0e680af4eafd", size = 11183, upload-time = "2025-06-14T20:54:46.325Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f0/5d/2d45351564273c1bd24ffc691d0d932b0cdef5373cc0f0510239b93d5913/pyobjc_framework_securityinterface-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0232f947b4f906097a5d758305097a8688835a52e0721b75ae3f1180eac30f50", size = 10885, upload-time = "2025-06-14T20:54:47.03Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ae/80/7b8dce55a83d1f6ed056f6dd5ec0a927ec0e4fbe60eba05ef1816cc0d959/pyobjc_framework_securityinterface-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2c20bedead75de7bf1f2ceda562755f64c70ee86180ed45480dc9dbc55609a0b", size = 11225, upload-time = "2025-06-14T20:54:47.731Z" },
]

[[package]]
name = "pyobjc-framework-securityui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/5b/3b5585d56e0bcaba82e0661224bbc7aaf29fba6b10498971dbe08b2b490a/pyobjc_framework_securityui-11.1.tar.gz", hash = "sha256:e80c93e8a56bf89e4c0333047b9f8219752dd6de290681e9e2e2b2e26d69e92d", size = 12179, upload-time = "2025-06-14T20:58:30.928Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d0/a4/c9fcc42065b6aed73b14b9650c1dc0a4af26a30d418cbc1bab33621b461c/pyobjc_framework_securityui-11.1-py2.py3-none-any.whl", hash = "sha256:3cdb101b03459fcf8e4064b90021d06761003f669181e02f43ff585e6ba2403d", size = 3581, upload-time = "2025-06-14T20:54:49.474Z" },
]

[[package]]
name = "pyobjc-framework-sensitivecontentanalysis"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/56/7b/e28f6b30d99e9d464427a07ada82b33cd3292f310bf478a1824051d066b9/pyobjc_framework_sensitivecontentanalysis-11.1.tar.gz", hash = "sha256:5b310515c7386f7afaf13e4632d7d9590688182bb7b563f8026c304bdf317308", size = 12796, upload-time = "2025-06-14T20:58:31.488Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3c/63/76a939ecac74ca079702165330c692ad2c05ff9b2b446a72ddc8cdc63bb9/pyobjc_framework_sensitivecontentanalysis-11.1-py2.py3-none-any.whl", hash = "sha256:dbb78f5917f986a63878bb91263bceba28bd86fc381bad9461cf391646db369f", size = 3852, upload-time = "2025-06-14T20:54:50.75Z" },
]

[[package]]
name = "pyobjc-framework-servicemanagement"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/20/c6/32e11599d9d232311607b79eb2d1d21c52eaaf001599ea85f8771a933fa2/pyobjc_framework_servicemanagement-11.1.tar.gz", hash = "sha256:90a07164da49338480e0e135b445acc6ae7c08549a2037d1e512d2605fedd80a", size = 16645, upload-time = "2025-06-14T20:58:32.062Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/f1/222462f5afcb6cb3c1fc9e6092dfcffcc7eb9db8bd2cef8c1743a22fbe95/pyobjc_framework_servicemanagement-11.1-py2.py3-none-any.whl", hash = "sha256:104f56557342a05ad68cd0c9daf63b7f4678957fe1f919f03a872f1607a50710", size = 5338, upload-time = "2025-06-14T20:54:51.614Z" },
]

[[package]]
name = "pyobjc-framework-sharedwithyou"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-sharedwithyoucore" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/fe/a5/e299fbd0c13d4fac9356459f21372f6eef4279d0fbc99ba316d88dfbbfb4/pyobjc_framework_sharedwithyou-11.1.tar.gz", hash = "sha256:ece3a28a3083d0bcad0ac95b01f0eb699b9d2d0c02c61305bfd402678753ff6e", size = 34216, upload-time = "2025-06-14T20:58:32.75Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/57/44/211e1f18676e85d3656671fc0c954ced2cd007e55f1b0b6b2e4d0a0852eb/pyobjc_framework_sharedwithyou-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:99e1749187ae370be7b9c55dd076d1b8143f0d8db3e83f52540586f32e7abb33", size = 8740, upload-time = "2025-06-14T20:54:53.879Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6f/da/1a2f2ae024e0206e1bcaba27aac2ebadf8bceb0ee05d03be2250e8c3d1a3/pyobjc_framework_sharedwithyou-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c1a1770aa2c417f17010623414fb12943570baa726d8780dd7446ba5bcee8c3d", size = 8759, upload-time = "2025-06-14T20:54:54.631Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/48/85/d54efa902f5dd18a99478eb4fd0befda07dcd2672b1c3ed00ec88280fed0/pyobjc_framework_sharedwithyou-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:63b1cb673b844ebfeddc032d0539f913bbd6b67ab2a310a1fcff7842dba9c714", size = 8909, upload-time = "2025-06-14T20:54:55.359Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/df/a0/03d0277bae4b49f9ec6dd078c7b66ffbeca71ffe47c206222697a7a563e2/pyobjc_framework_sharedwithyou-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:481362f0bde6def86634fc687abe6f4dee650c09c22b48bfe5af5322f9947cef", size = 8807, upload-time = "2025-06-14T20:54:56.041Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f0/66/0873bad696dfa6f8b597c9de5b0a1e1529f4ed21bf54c8389ec43499298d/pyobjc_framework_sharedwithyou-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:70421a8fd326afd99eeae273b693a7b4d2d200c38e883d8219a84123a4ba0861", size = 8955, upload-time = "2025-06-14T20:54:57.351Z" },
]

[[package]]
name = "pyobjc-framework-sharedwithyoucore"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/79/a3/1ca6ff1b785772c7c5a38a7c017c6f971b1eda638d6a0aab3bbde18ac086/pyobjc_framework_sharedwithyoucore-11.1.tar.gz", hash = "sha256:790050d25f47bda662a9f008b17ca640ac2460f2559a56b17995e53f2f44ed73", size = 29459, upload-time = "2025-06-14T20:58:33.422Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/70/3b2e13fcf393aa434b1cf5c29c6aaf65ee5b8361254df3a920ed436bb5e4/pyobjc_framework_sharedwithyoucore-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dd18c588b29de322c25821934d6aa6d2bbbdbb89b6a4efacdb248b4115fc488d", size = 8512, upload-time = "2025-06-14T20:55:00.411Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b7/fc/feb2912fb9c7bbeb2099d2cb42ad28055c6e29504fcb92bd8a011fcba66a/pyobjc_framework_sharedwithyoucore-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a3fb0e745fd022fed48cc9a5e0dcbf8d1abcb5bfc192150e3a2584f4351791fc", size = 8527, upload-time = "2025-06-14T20:55:01.112Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f1/3f/0a8aa5d1b0eb07508c42e900d82a89e096b79fcafcd55e966d4d45476ae5/pyobjc_framework_sharedwithyoucore-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6aee3df8bed97a74e1f79609f9884edcaab2d305db20bdcae39e47b3e513c559", size = 8672, upload-time = "2025-06-14T20:55:01.801Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/64/f4/582ca62f3b154a5a0c46854c329aae07dddeadbced077394211644d4862b/pyobjc_framework_sharedwithyoucore-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5a45c562c99017f8e057d4080012b63a9bb660c696334707c54d7b4018ca1017", size = 8569, upload-time = "2025-06-14T20:55:02.52Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/98/3a/b64eccedc362d0427cd67dfa4531b3eb935a2c31419f3f5803f40dcb0803/pyobjc_framework_sharedwithyoucore-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e19bfc74f392546ca4b7ea5271d4802617445ad493428370eafd3cddd4d977e", size = 8719, upload-time = "2025-06-14T20:55:03.624Z" },
]

[[package]]
name = "pyobjc-framework-shazamkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/de/08/ba739b97f1e441653bae8da5dd1e441bbbfa43940018d21edb60da7dd163/pyobjc_framework_shazamkit-11.1.tar.gz", hash = "sha256:c6e3c9ab8744d9319a89b78ae6f185bb5704efb68509e66d77bcd1f84a9446d6", size = 25797, upload-time = "2025-06-14T20:58:34.086Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/89/b7/594b8bdc406603a7a07cdb33f2be483fed16aebc35aeb087385fc9eca844/pyobjc_framework_shazamkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b323f5409b01711aa2b6e2113306084fab2cc83fa57a0c3d55bd5876358b68d8", size = 8560, upload-time = "2025-06-14T20:55:07.564Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8c/fa/49ba8d1f9e257a12267773d6682e170fba441c7ea72d6fe58da9f4bf6f10/pyobjc_framework_shazamkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8bac17f285742e0f13a54c7085ef3035d8034ffc43d18d3d68fb41283c5064ff", size = 8573, upload-time = "2025-06-14T20:55:08.42Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/22/47/eeae6a31a41cbaf29081145b8f54ddebf68a5eba19626dd9ba2c00fdc92b/pyobjc_framework_shazamkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b3304c3a67e3722b895d874f215dd4277b49cedddb72fa780a791ef79e5c3d45", size = 8726, upload-time = "2025-06-14T20:55:09.447Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/72/e4e4bca07808f0a930955ddfdd10cf6322096fced76bf06b52d379df850c/pyobjc_framework_shazamkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ef51f461672234076b3791ad4be05adad20a2e24b9d7d93acd7bf18d7f9b1714", size = 8610, upload-time = "2025-06-14T20:55:10.14Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c4/f2/31e186b99ccf22cbceddea58edfdcbef6a336c12326e198e7c6fd18b5938/pyobjc_framework_shazamkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f7d191fb187dbb05e3f88f546d5207618d65e270d7a4316b51b1171cc491e268", size = 8766, upload-time = "2025-06-14T20:55:10.833Z" },
]

[[package]]
name = "pyobjc-framework-social"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/2e/cc7707b7a40df392c579087947049f3e1f0e00597e7151ec411f654d8bef/pyobjc_framework_social-11.1.tar.gz", hash = "sha256:fbc09d7b00dad45b547f9b2329f4dcee3f5a50e2348de1870de0bd7be853a5b7", size = 14540, upload-time = "2025-06-14T20:58:35.116Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/86/1d/e1026c082a66075dbb7e57983c0aaaed3ee09f06c346743e8af24d1dc21a/pyobjc_framework_social-11.1-py2.py3-none-any.whl", hash = "sha256:ab5878c47d7a0639704c191cee43eeb259e09688808f0905c42551b9f79e1d57", size = 4444, upload-time = "2025-06-14T20:55:12.536Z" },
]

[[package]]
name = "pyobjc-framework-soundanalysis"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e0/d4/b9497dbb57afdf0d22f61bb6e776a6f46cf9294c890448acde5b46dd61f3/pyobjc_framework_soundanalysis-11.1.tar.gz", hash = "sha256:42cd25b7e0f343d8b59367f72b5dae96cf65696bdb8eeead8d7424ed37aa1434", size = 16539, upload-time = "2025-06-14T20:58:35.813Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/13/b4/7e8cf3a02e615239568fdf12497233bbd5b58082615cd28a0c7cd4636309/pyobjc_framework_soundanalysis-11.1-py2.py3-none-any.whl", hash = "sha256:6cf983c24fb2ad2aa5e7499ab2d30ff134d887fe91fd2641acf7472e546ab4e5", size = 4161, upload-time = "2025-06-14T20:55:13.342Z" },
]

[[package]]
name = "pyobjc-framework-speech"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/67/76/2a1fd7637b2c662349ede09806e159306afeebfba18fb062ad053b41d811/pyobjc_framework_speech-11.1.tar.gz", hash = "sha256:d382977208c3710eacea89e05eae4578f1638bb5a7b667c06971e3d34e96845c", size = 41179, upload-time = "2025-06-14T20:58:36.43Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/59/267f4699055beb39723ccbff70909ec3851e4adf17386f6ad85e5d983780/pyobjc_framework_speech-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7726eff52cfa9cc7178ddcd1285cbc23b5f89ee55b4b850b0d2e90bb4f8e044b", size = 9180, upload-time = "2025-06-14T20:55:16.556Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ea/a6/c394c3973c42d86c7b0c5c673c5ce65d10671e59e174f1ba4e7ab61ae5df/pyobjc_framework_speech-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3c80670dbad921bf1d4954a9de29525acb53ee84e064a95fbbdfddff1db2f14f", size = 9198, upload-time = "2025-06-14T20:55:17.581Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/95/e9/3e47e2e3337080e45dd9153c7f465d16c40ce74b11ac53c4663554dab0bd/pyobjc_framework_speech-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f19778a4ace37c538a34a10ac1f595c80b83489210e6fa60c703399aee264c7e", size = 9355, upload-time = "2025-06-14T20:55:18.27Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b1/81/dfc795916cfb5d9eb98809e93b380948422d3901ce60ec168681530b6fd5/pyobjc_framework_speech-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f36ca8a3cfc12b7a5cdf00712eec3ad0fac34e3da36b5737c5302e224525aa70", size = 9249, upload-time = "2025-06-14T20:55:18.961Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e0/cd/29d5a50d9c596eef5d9b9c1442169908e99bc79edc58b573e393829b1f6b/pyobjc_framework_speech-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:80e577e3dfc1c10a1280deae172cdb64e105f99f47343099e3968b720a3f68da", size = 9401, upload-time = "2025-06-14T20:55:20.242Z" },
]

[[package]]
name = "pyobjc-framework-spritekit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/16/02/2e253ba4f7fad6efe05fd5fcf44aede093f6c438d608d67c6c6623a1846d/pyobjc_framework_spritekit-11.1.tar.gz", hash = "sha256:914da6e846573cac8db5e403dec9a3e6f6edf5211f9b7e429734924d00f65108", size = 130297, upload-time = "2025-06-14T20:58:37.113Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f1/fe/39d92bf40ec7a6116f89fd95053321f7c00c50c10d82b9adfa0f9ebdb10c/pyobjc_framework_spritekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8b470a890db69e70ef428dfff88da499500fca9b2d44da7120dc588d13a2dbdb", size = 17776, upload-time = "2025-06-14T20:55:23.639Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3f/c1/56490cce24e34e8c4c8c6a0f4746cd3a8bb5c2403e243c99f4dfa0cd147f/pyobjc_framework_spritekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2277e74d7be426181ae5ca7dd9d6c776426e8e825ad83b6046a7cb999015f27d", size = 17798, upload-time = "2025-06-14T20:55:24.407Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/75/dc/2ddd3aec417ebb92fd37f687c3e41e051d5e8b761bf2af63b1eb21e20cf4/pyobjc_framework_spritekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d6ea27fc202b40945729db50fdc6f75a0a11a07149febf4b99e14caf96ef33b0", size = 18068, upload-time = "2025-06-14T20:55:25.541Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f1/db/f26835b6c4e169bb451878973e109deb5c8e14c41042d97795200f4d3bbb/pyobjc_framework_spritekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e04d0825109a0158e551e9e2a61c56e83eadfdc5a44a47b64cb410b0498d33be", size = 17835, upload-time = "2025-06-14T20:55:26.295Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4c/c3/e920aacda0bf97b37396eafb93676f359a8407a8e04fae6f9c80c25ba922/pyobjc_framework_spritekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e3673196b7cbc007e4aa7f14d711f3cda00e32e120bc4f6e896d54edd517c61", size = 18092, upload-time = "2025-06-14T20:55:27.04Z" },
]

[[package]]
name = "pyobjc-framework-storekit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/44/a0/58cab9ebc9ac9282e1d4734b1987d1c3cd652b415ec3e678fcc5e735d279/pyobjc_framework_storekit-11.1.tar.gz", hash = "sha256:85acc30c0bfa120b37c3c5ac693fe9ad2c2e351ee7a1f9ea6f976b0c311ff164", size = 76421, upload-time = "2025-06-14T20:58:37.86Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ac/61/6404aac6857ea43798882333bcc26bfd3c9c3a1efc7a575cbf3e53538e2a/pyobjc_framework_storekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:5ca3373272b6989917c88571ca170ce6d771180fe1a2b44c7643fe084569b93e", size = 11868, upload-time = "2025-06-14T20:55:30.454Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6b/52/23acdf128a5b04059b2a3b38928afbff0afb50da439b597e25cdff1e9148/pyobjc_framework_storekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2e2607116b0d53d7fda2fc48e37b1deb1d26a60e7b723a6b7c391a3f48b2ac3b", size = 11882, upload-time = "2025-06-14T20:55:31.523Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/48/04/e7407f5c11a56c9a3a6b4328ec95dbf01ea6f88ac0ff5dc5089e9c8d0a61/pyobjc_framework_storekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4944bd1fd01f486623453b68accf4445d3c5686714820c8329a0c4e4672d6fff", size = 12129, upload-time = "2025-06-14T20:55:32.213Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7a/de/8910a6f54647c0adc2aeb6846afc94a99d17470dd3d905e8b1caeccfcd98/pyobjc_framework_storekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d312c392962e15fc842d11b0f7d937e3bd9f3ed3a80f7a6be77518475564f04d", size = 11939, upload-time = "2025-06-14T20:55:33.075Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/12/c04fa481f7ec80beaff532734dde19303133547ae16414934d05d0df046f/pyobjc_framework_storekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:be6c894a9f9c2b40e300005c3a3cf46f352e1711f65c0b7a8dd5035d1f6333aa", size = 12121, upload-time = "2025-06-14T20:55:34.087Z" },
]

[[package]]
name = "pyobjc-framework-symbols"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/cd/af/7191276204bd3e7db1d0a3e490a869956606f77f7a303a04d92a5d0c3f7b/pyobjc_framework_symbols-11.1.tar.gz", hash = "sha256:0e09b7813ef2ebdca7567d3179807444dd60f3f393202b35b755d4e1baf99982", size = 13377, upload-time = "2025-06-14T20:58:38.542Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9a/6a/c91f64ef9b8cd20245b88e392c66cb2279c511724f4ea2983d92584d6f3e/pyobjc_framework_symbols-11.1-py2.py3-none-any.whl", hash = "sha256:1de6fc3af15fc8d5fd4869663a3250311844ec33e99ec8a1991a352ab61d641d", size = 3312, upload-time = "2025-06-14T20:55:35.456Z" },
]

[[package]]
name = "pyobjc-framework-syncservices"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coredata" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/69/45/cd9fa83ed1d75be7130fb8e41c375f05b5d6621737ec37e9d8da78676613/pyobjc_framework_syncservices-11.1.tar.gz", hash = "sha256:0f141d717256b98c17ec2eddbc983c4bd39dfa00dc0c31b4174742e73a8447fe", size = 57996, upload-time = "2025-06-14T20:58:39.146Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/01/2b/6d7d65c08a9c51eed12eb7f83eaa48deaed621036f77221b3b0346c3f6c2/pyobjc_framework_syncservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:03124c8c7c7ce837f51e1c9bdcf84c6f1d5201f92c8a1c172ec34908d5e57415", size = 13496, upload-time = "2025-06-14T20:55:37.83Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/99/7b/88e89b81b5a6ee7da3b452c1619ec22936a8dd4384afd67f6019472655b8/pyobjc_framework_syncservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:711d493c7967682bee605c5909a49d268d9b3dd3cb7a71d8ab5dbe01a069eb44", size = 13511, upload-time = "2025-06-14T20:55:38.55Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/bf/3c/6056913cea9fce52f77649b81c54c6282f2eb1b26e7ca17c5c1015123375/pyobjc_framework_syncservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a0ff222472b2cb5c345c92ae4bde245f4181843379f4fd9462cd5c096ed7b2f1", size = 13681, upload-time = "2025-06-14T20:55:39.279Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/63/b1/c9f74441515efd2b05b797df09fff37b61aa583dac6462152063ab47b80d/pyobjc_framework_syncservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:24c2b62e94d9e0e5e64abbf6d1f9994212b2a5cb8cad5a8d0394d694b20731b5", size = 13576, upload-time = "2025-06-14T20:55:39.994Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/36/0f/812a2151539aa46363fe4abaad99344380a5c2287840c98a5a021bf3ed0f/pyobjc_framework_syncservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e5b29d6e8fe5b0015dcac5485e4fe6ede35bae7beeb647fb81d86120365029ea", size = 13754, upload-time = "2025-06-14T20:55:41.223Z" },
]

[[package]]
name = "pyobjc-framework-systemconfiguration"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e2/3d/41590c0afc72e93d911348fbde0c9c1071ff53c6f86df42df64b21174bb9/pyobjc_framework_systemconfiguration-11.1.tar.gz", hash = "sha256:f30ed0e9a8233fecb06522e67795918ab230ddcc4a18e15494eff7532f4c3ae1", size = 143410, upload-time = "2025-06-14T20:58:39.917Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b9/61/0e9841bf1c7597f380a6dcefcc9335b6a909f20d9bdf07910cddc8552b42/pyobjc_framework_systemconfiguration-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6881929b828a566bf1349f09db4943e96a2b33f42556e1f7f6f28b192420f6fc", size = 21639, upload-time = "2025-06-14T20:55:44.678Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1c/eb/4480a1ab5baba4b9e75bb7f4f667073db5702cf521ddc99941575167585d/pyobjc_framework_systemconfiguration-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ab2ff52e4228f42182b7ef398d0da504f9f8f4a889963422af9aa1f495668db2", size = 21646, upload-time = "2025-06-14T20:55:45.426Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b7/00/40d433a160c4d3c156008d375aa0279f46343c69cecb464e59ab1a0b3063/pyobjc_framework_systemconfiguration-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:c236f19cadc9fff56c0afb3e4ad6f8c8e11c5679e31ed413fe6876bf2ea73353", size = 22059, upload-time = "2025-06-14T20:55:46.203Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/60/d0/18ad65359d0fd71c67f14b02bf03efdd6e472185204c82f5885343798d52/pyobjc_framework_systemconfiguration-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ef266e9f83c2fc9a999709626138b427ff052a0acf4851d797c3a7654878c046", size = 21667, upload-time = "2025-06-14T20:55:47.303Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e6/cf/4dcf61dd20bfa8d95e4328f431b59119bc2118da9dc570738428ec556b80/pyobjc_framework_systemconfiguration-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:b994c613b5bea9f1c9a64f57f373563c7f424ffae5e4cb20e76c8448a35543f7", size = 22056, upload-time = "2025-06-14T20:55:48.055Z" },
]

[[package]]
name = "pyobjc-framework-systemextensions"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/57/4609fd9183383616b1e643c2489ad774335f679523a974b9ce346a6d4d5b/pyobjc_framework_systemextensions-11.1.tar.gz", hash = "sha256:8ff9f0aad14dcdd07dd47545c1dd20df7a286306967b0a0232c81fcc382babe6", size = 23062, upload-time = "2025-06-14T20:58:40.686Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/76/40/d9be444b39ec12d68b5e4f712b71d6c00d654936ff5744ea380c1bfabf06/pyobjc_framework_systemextensions-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:3a2b1e84e4a118bfe13efb9f2888b065dc937e2a7e60afd4d0a82b51b8301a10", size = 9130, upload-time = "2025-06-14T20:55:51.127Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7d/23/f615d69b3a86e75af234149fc12c8dfde8f346148e4eb185696a9c87e824/pyobjc_framework_systemextensions-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2ed65857244f18b88107e5d3ea8ea21c9da662490895b430e376423ee7c0b963", size = 9154, upload-time = "2025-06-14T20:55:51.798Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/3c/08/2719c95d57f404d880c80da4250ff122ff318307e7a9b8ceef54d56fdb7f/pyobjc_framework_systemextensions-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:9aa7595de4f8f6a252c50419c0343f7326c6a4de47da5b933a17880d1cadfa36", size = 9315, upload-time = "2025-06-14T20:55:52.494Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/88/ff/a984a96f49b27d9c79ab97aa484bac27d3b4f1de14b9a1080de3622e63f1/pyobjc_framework_systemextensions-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:97c1b5f415f3981d0426516e014e94392f054f3898252bf6c88c3f50700c1d70", size = 9204, upload-time = "2025-06-14T20:55:53.173Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d9/57/574b1c59afac30e605c476c5911a69e70d338adf5ff810042f5d55e77871/pyobjc_framework_systemextensions-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1801413066d1cbf2a0319e228060820c51ea0fb27aec339716d8c82f2e1b3125", size = 9366, upload-time = "2025-06-14T20:55:54.251Z" },
]

[[package]]
name = "pyobjc-framework-threadnetwork"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e7/a4/5400a222ced0e4f077a8f4dd0188e08e2af4762e72ed0ed39f9d27feefc9/pyobjc_framework_threadnetwork-11.1.tar.gz", hash = "sha256:73a32782f44b61ca0f8a4a9811c36b1ca1cdcf96c8a3ba4de35d8e8e58a86ad5", size = 13572, upload-time = "2025-06-14T20:58:41.311Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b0/f0/b7a577d00bdb561efef82b046a75f627a60de53566ab2d9e9ddd5bd11b66/pyobjc_framework_threadnetwork-11.1-py2.py3-none-any.whl", hash = "sha256:55021455215a0d3ad4e40152f94154e29062e73655558c5f6e71ab097d90083e", size = 3751, upload-time = "2025-06-14T20:55:55.643Z" },
]

[[package]]
name = "pyobjc-framework-uniformtypeidentifiers"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/c5/4f/066ed1c69352ccc29165f45afb302f8c9c2b5c6f33ee3abfa41b873c07e5/pyobjc_framework_uniformtypeidentifiers-11.1.tar.gz", hash = "sha256:86c499bec8953aeb0c95af39b63f2592832384f09f12523405650b5d5f1ed5e9", size = 20599, upload-time = "2025-06-14T20:58:41.945Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/de/3b/b63b8137dd9f455d5abece6702c06c6b613fac6fda1319aaa2f79d00c380/pyobjc_framework_uniformtypeidentifiers-11.1-py2.py3-none-any.whl", hash = "sha256:6e2e8ea89eb8ca03bc2bc8e506fff901e71d916276475c8d81fbf0280059cb4c", size = 4891, upload-time = "2025-06-14T20:55:56.432Z" },
]

[[package]]
name = "pyobjc-framework-usernotifications"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/b4/4c/e7e180fcd06c246c37f218bcb01c40ea0213fde5ace3c09d359e60dcaafd/pyobjc_framework_usernotifications-11.1.tar.gz", hash = "sha256:38fc763afa7854b41ddfca8803f679a7305d278af8a7ad02044adc1265699996", size = 55428, upload-time = "2025-06-14T20:58:42.572Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/03/af/a54e343a7226dc65a65f7a561c060f8c96cb9f92f41ce2242d20d82ae594/pyobjc_framework_usernotifications-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ce6006989fd4a59ec355f6797ccdc9946014ea5241ff7875854799934dbba901", size = 9606, upload-time = "2025-06-14T20:55:59.088Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d1/fb/ae1ea7f7c511714c1502fa9c4856c6b3dfe110ff7cc094070fec5ad496b8/pyobjc_framework_usernotifications-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9efa3004059a8fe3f3c52f638f0401dbcdbc7b2f539587c8868da2486a64d674", size = 9628, upload-time = "2025-06-14T20:55:59.807Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/e5/46/4934930848d74aeea32435378154501fcb3dbd77f759c4aa09b99e094310/pyobjc_framework_usernotifications-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:62a4bd242b761a6f00a4374a369391346d225d68be07691e042ec7db452084c8", size = 9793, upload-time = "2025-06-14T20:56:00.496Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/f2/f7/fadd62a479322bc8bf20684c6a87a1eb40b28c03899a8cc3d5b6fe781d93/pyobjc_framework_usernotifications-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:dcdcb657d2fa47108e4ef93ec3320025576857e8f69a15f082f5eda930b35e86", size = 9666, upload-time = "2025-06-14T20:56:01.176Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/72/c3/406d196d094cf8c30bbc815a8ca8ef57bfa21c2494f93ff1125f78f8a922/pyobjc_framework_usernotifications-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:bad5e650c014757159523466e5b2c127e066045e2a5579a5cac9aeca46bda017", size = 9852, upload-time = "2025-06-14T20:56:01.871Z" },
]

[[package]]
name = "pyobjc-framework-usernotificationsui"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-usernotifications" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/d2/c4/03d97bd3adcee9b857533cb42967df0d019f6a034adcdbcfca2569d415b2/pyobjc_framework_usernotificationsui-11.1.tar.gz", hash = "sha256:18e0182bddd10381884530d6a28634ebb3280912592f8f2ad5bac2a9308c6a65", size = 14123, upload-time = "2025-06-14T20:58:43.267Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/9d/2c/0bb489b5ac4daf83b113018701ce30a0cb4bf47c615c92c5844a16e0a012/pyobjc_framework_usernotificationsui-11.1-py2.py3-none-any.whl", hash = "sha256:b84d73d90ab319acf8fad5c59b7a5e2b6023fbb2efd68c58b532e3b3b52f647a", size = 3914, upload-time = "2025-06-14T20:56:03.978Z" },
]

[[package]]
name = "pyobjc-framework-videosubscriberaccount"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/aa/00/cd9d93d06204bbb7fe68fb97022b0dd4ecdf8af3adb6d70a41e22c860d55/pyobjc_framework_videosubscriberaccount-11.1.tar.gz", hash = "sha256:2dd78586260fcee51044e129197e8bf2e157176e02babeec2f873afa4235d8c6", size = 28856, upload-time = "2025-06-14T20:58:43.903Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4b/dc/b409dee6dd58a5db2e9a681bde8894c9715468689f18e040f7d252794c3d/pyobjc_framework_videosubscriberaccount-11.1-py2.py3-none-any.whl", hash = "sha256:d5a95ae9f2a6f0180a5bbb10e76c064f0fd327aae00a2fe90aa7b65ed4dad7ef", size = 4695, upload-time = "2025-06-14T20:56:06.027Z" },
]

[[package]]
name = "pyobjc-framework-videotoolbox"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e5/e3/df9096f54ae1f27cab8f922ee70cbda5d80f8c1d12734c38580829858133/pyobjc_framework_videotoolbox-11.1.tar.gz", hash = "sha256:a27985656e1b639cdb102fcc727ebc39f71bb1a44cdb751c8c80cc9fe938f3a9", size = 88551, upload-time = "2025-06-14T20:58:44.566Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/1f/cf/569babadbf1f9598f62c400ee02da19d4ab5f36276978c81080999399df9/pyobjc_framework_videotoolbox-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c55285c3c78183fd2a092d582e30b562777a82985cccca9e7e99a0aff2601591", size = 17432, upload-time = "2025-06-14T20:56:08.457Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b1/32/1a3d1a448d3cbcaf5c2a4ceaaad32817df21739099e187bbe6e3fd03d6fd/pyobjc_framework_videotoolbox-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:65a96385e80cb9ad3eab7d1f3156452ff805a925c9ca287ff1491a97cca191ba", size = 17450, upload-time = "2025-06-14T20:56:09.239Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/64/d9/530b561bea7b8690ca976570466e42fa226fc60fe3fef3d14beaf719dc99/pyobjc_framework_videotoolbox-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e282cb07f6a51647ac19a3b5d31e26f1619285bac24171e403921d671e4756d9", size = 17668, upload-time = "2025-06-14T20:56:09.98Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/21/de/478ead66538d665860bfc8fdb7c66a93bc07a9b32bd4150ee181bd16a66b/pyobjc_framework_videotoolbox-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:31acfb12cea4f0624ecb92e74404f15e2755fbf0a3f4133dc93add44cf4a6a9f", size = 17452, upload-time = "2025-06-14T20:56:10.738Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/6d/32/bd465a698e680f95df87b3948dc4ced5f95dc813a88987355ffee5e1638c/pyobjc_framework_videotoolbox-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:0e54bd6cfcbdda4add24e8e873baab11dfb436633100cc6664f3c068e615a6ff", size = 17645, upload-time = "2025-06-14T20:56:11.507Z" },
]

[[package]]
name = "pyobjc-framework-virtualization"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/f1/ff/57214e8f42755eeaad516a7e673dae4341b8742005d368ecc22c7a790b0b/pyobjc_framework_virtualization-11.1.tar.gz", hash = "sha256:4221ee5eb669e43a2ff46e04178bec149af2d65205deb5d4db5fa62ea060e022", size = 78633, upload-time = "2025-06-14T20:58:45.358Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/c8/4f/fe1930f4ce2c7d2f4c34bb53adf43f412bc91364e8e4cb450a7c8a6b8b59/pyobjc_framework_virtualization-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:59df6702b3e63200752be7d9c0dc590cb4c3b699c886f9a8634dd224c74b3c3c", size = 13084, upload-time = "2025-06-14T20:56:14.617Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/4f/33/6d9f4177983d8894d217b212c25cbb91004cb1103c865961f03360aff68b/pyobjc_framework_virtualization-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:12a5ef32d2b7a56b675ea34fcb68bb9dddb7cf2c0a5ac5131f35551767bdacf1", size = 13093, upload-time = "2025-06-14T20:56:15.322Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/78/af/b9e1b6fa9afb4a6557e3bc1e7e8409108ecf416db5a8a9c6ef4d25dd16af/pyobjc_framework_virtualization-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:790bd2e42e8c5890319f8c576d5e171f87f95655e6fc55cf19a5f85f9e23558a", size = 13284, upload-time = "2025-06-14T20:56:16.052Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/19/d7/9cadb62789974cb7ff65435e4b000d34cf9ec43e46ec2eb73de1620ab6a0/pyobjc_framework_virtualization-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5f35d823003a613bde27c2c699a8a7de45dc2bdd2e1121e0c4a337b877dfc64e", size = 13111, upload-time = "2025-06-14T20:56:17.128Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2c/ee/39e84b673a33a10f518ecf5f7398a6a6864d2f23c79996c36809677678a1/pyobjc_framework_virtualization-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:b2e7ab5204fe80249dd8d031b761cf9c0106d0d5e61d88930e0f334f5060d820", size = 13299, upload-time = "2025-06-14T20:56:17.849Z" },
]

[[package]]
name = "pyobjc-framework-vision"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreml" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/40/a8/7128da4d0a0103cabe58910a7233e2f98d18c590b1d36d4b3efaaedba6b9/pyobjc_framework_vision-11.1.tar.gz", hash = "sha256:26590512ee7758da3056499062a344b8a351b178be66d4b719327884dde4216b", size = 133721, upload-time = "2025-06-14T20:58:46.095Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a2/b5/54c0227a695557ea3065bc035b20a5c256f6f3b861e095eee1ec4b4d8cee/pyobjc_framework_vision-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:df076c3e3e672887182953efc934c1f9683304737e792ec09a29bfee90d2e26a", size = 16829, upload-time = "2025-06-14T20:56:21.355Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/20/cf/58ace43525ab073b39df9a740e855ebe83ed78f041d619644af3c60d9013/pyobjc_framework_vision-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1e5617e37dd2a7cff5e69e9aab039ea74b39ccdc528f6c828f2b60c1254e61e5", size = 16852, upload-time = "2025-06-14T20:56:22.081Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/99/c3/4aeaac1d53766125870aadbe3a4a02d4bca373b18753d32281f77e095976/pyobjc_framework_vision-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:dfd148a6df30ac70a9c41dd90a6c8f8c7f339bd9ca6829629a902f272e02b6b4", size = 16993, upload-time = "2025-06-14T20:56:22.818Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/75/29/bd70761b455067f1f0cb90a7c1983152b0e42b1f05ff91aa42c994a3f97d/pyobjc_framework_vision-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d1f8fdccc6135fdbfd66d8f21240d6c84465cb8e116a8e5b43601aed020051e5", size = 16847, upload-time = "2025-06-14T20:56:23.572Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/23/e1/72d2410377497b04ecd9718d8784a9d31bce36bbce0cb77c4e4fbcce7070/pyobjc_framework_vision-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:d00830c71a30fc893b3c5ee65119c7e5e5a95a16af53b8e56a0e58cff57e3b56", size = 16995, upload-time = "2025-06-14T20:56:24.335Z" },
]

[[package]]
name = "pyobjc-framework-webkit"
version = "11.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/92/04/fb3d0b68994f7e657ef00c1ac5fc1c04ae2fc7ea581d647f5ae1f6739b14/pyobjc_framework_webkit-11.1.tar.gz", hash = "sha256:27e701c7aaf4f24fc7e601a128e2ef14f2773f4ab071b9db7438dc5afb5053ae", size = 717102, upload-time = "2025-06-14T20:58:47.461Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/8a/7e/fa2c18c0c0f9321e5036e54b9da7a196956b531e50fe1a76e7dfdbe8fac2/pyobjc_framework_webkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1a6e6f64ca53c4953f17e808ecac11da288d9a6ade738156ba161732a5e0c96a", size = 51464, upload-time = "2025-06-14T20:56:27.653Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7a/8d/66561d95b00b8e57a9d5725ae34a8d9ca7ebeb776f13add989421ff90279/pyobjc_framework_webkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1d01008756c3912b02b7c02f62432467fbee90a93e3b8e31fa351b4ca97c9c98", size = 51495, upload-time = "2025-06-14T20:56:28.464Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/db/c3/e790b518f84ea8dfbe32a9dcb4d8611b532de08057d19f853c1890110938/pyobjc_framework_webkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:864f9867a2caaeaeb83e5c0fa3dcf78169622233cf93a9a5eeb7012ced3b8076", size = 51985, upload-time = "2025-06-14T20:56:29.303Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d7/4f/194e3e7c01861a5e46dfe9e1fa28ad01fd07190cb514e41a7dcf1f0b7031/pyobjc_framework_webkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:13b774d4244734cb77bf3c3648149c163f62acaa105243d7c48bb3fd856b5628", size = 52248, upload-time = "2025-06-14T20:56:30.158Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/31/09/28884e7c10d3a76a76c2c8f55369dd96a90f0283800c68f5c764e1fb8e2e/pyobjc_framework_webkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c1c00d549ab1d50e3d7e8f5f71352b999d2c32dc2365c299f317525eb9bff916", size = 52725, upload-time = "2025-06-14T20:56:30.993Z" },
]

[[package]]
name = "pyperclip"
version = "1.9.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/30/23/2f0a3efc4d6a32f3b63cdff36cd398d9701d26cda58e3ab97ac79fb5e60d/pyperclip-1.9.0.tar.gz", hash = "sha256:b7de0142ddc81bfc5c7507eea19da920b92252b548b96186caf94a5e2527d310", size = 20961, upload-time = "2024-06-18T20:38:48.401Z" }

[[package]]
name = "pyrect"
version = "0.2.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/cb/04/2ba023d5f771b645f7be0c281cdacdcd939fe13d1deb331fc5ed1a6b3a98/PyRect-0.2.0.tar.gz", hash = "sha256:f65155f6df9b929b67caffbd57c0947c5ae5449d3b580d178074bffb47a09b78", size = 17219, upload-time = "2022-03-16T04:45:52.36Z" }

[[package]]
name = "pyscreeze"
version = "1.0.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/ee/f0/cb456ac4f1a73723d5b866933b7986f02bacea27516629c00f8e7da94c2d/pyscreeze-1.0.1.tar.gz", hash = "sha256:cf1662710f1b46aa5ff229ee23f367da9e20af4a78e6e365bee973cad0ead4be", size = 27826, upload-time = "2024-08-20T23:03:07.291Z" }

[[package]]
name = "pystray"
version = "0.19.5"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "pillow" },
    { name = "pyobjc-framework-quartz", marker = "sys_platform == 'darwin'" },
    { name = "python-xlib", marker = "sys_platform == 'linux'" },
    { name = "six" },
]
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5c/64/927a4b9024196a4799eba0180e0ca31568426f258a4a5c90f87a97f51d28/pystray-0.19.5-py2.py3-none-any.whl", hash = "sha256:a0c2229d02cf87207297c22d86ffc57c86c227517b038c0d3c59df79295ac617", size = 49068, upload-time = "2023-09-17T13:44:26.872Z" },
]

[[package]]
name = "python-xlib"
version = "0.33"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/86/f5/8c0653e5bb54e0cbdfe27bf32d41f27bc4e12faa8742778c17f2a71be2c0/python-xlib-0.33.tar.gz", hash = "sha256:55af7906a2c75ce6cb280a584776080602444f75815a7aff4d287bb2d7018b32", size = 269068, upload-time = "2022-12-25T18:53:00.824Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fc/b8/ff33610932e0ee81ae7f1269c890f697d56ff74b9f5b2ee5d9b7fa2c5355/python_xlib-0.33-py2.py3-none-any.whl", hash = "sha256:c3534038d42e0df2f1392a1b30a15a4ff5fdc2b86cfa94f072bf11b10a164398", size = 182185, upload-time = "2022-12-25T18:52:58.662Z" },
]

[[package]]
name = "python3-xlib"
version = "0.15"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/ef/c6/2c5999de3bb1533521f1101e8fe56fd9c266732f4d48011c7c69b29d12ae/python3-xlib-0.15.tar.gz", hash = "sha256:dc4245f3ae4aa5949c1d112ee4723901ade37a96721ba9645f2bfa56e5b383f8", size = 132828, upload-time = "2014-05-31T12:28:59.603Z" }

[[package]]
name = "pytweening"
version = "1.2.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/79/0c/c16bc93ac2755bac0066a8ecbd2a2931a1735a6fffd99a2b9681c7e83e90/pytweening-1.2.0.tar.gz", hash = "sha256:243318b7736698066c5f362ec5c2b6434ecf4297c3c8e7caa8abfe6af4cac71b", size = 171241, upload-time = "2024-02-20T03:37:56.809Z" }

[[package]]
name = "pywin32-ctypes"
version = "0.2.3"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/85/9f/01a1a99704853cb63f253eea009390c88e7131c67e66a0a02099a8c917cb/pywin32-ctypes-0.2.3.tar.gz", hash = "sha256:d162dc04946d704503b2edc4d55f3dba5c1d539ead017afa00142c38b9885755", size = 29471, upload-time = "2024-08-14T10:15:34.626Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/de/3d/8161f7711c017e01ac9f008dfddd9410dff3674334c233bde66e7ba65bbf/pywin32_ctypes-0.2.3-py3-none-any.whl", hash = "sha256:8a1513379d709975552d202d942d9837758905c8d01eb82b8bcc30918929e7b8", size = 30756, upload-time = "2024-08-14T10:15:33.187Z" },
]

[[package]]
name = "requests"
version = "2.32.4"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e1/0a/929373653770d8a0d7ea76c37de6e41f11eb07559b103b1c02cafb3f7cf8/requests-2.32.4.tar.gz", hash = "sha256:27d0316682c8a29834d3264820024b62a36942083d52caf2f14c0591336d3422", size = 135258, upload-time = "2025-06-09T16:43:07.34Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl", hash = "sha256:27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c", size = 64847, upload-time = "2025-06-09T16:43:05.728Z" },
]

[[package]]
name = "rubicon-objc"
version = "0.5.1"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/e0/83/e57741dcf862a2581d53eccf8b11749c97f73d9754bbc538fb6c7b527da3/rubicon_objc-0.5.1.tar.gz", hash = "sha256:90bee9fc1de4515e17615e15648989b88bb8d4d2ffc8c7c52748272cd7f30a66", size = 174639, upload-time = "2025-06-03T06:33:50.822Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/58/0a/e451c3dbda38dd6abab1fd16c3b35623fc0635dffcbbf97f1acc55a58508/rubicon_objc-0.5.1-py3-none-any.whl", hash = "sha256:17092756241b8370231cfaad45ad6e8ce99534987f2acbc944d65df5bdf8f6cd", size = 63323, upload-time = "2025-06-03T06:33:48.863Z" },
]

[[package]]
name = "setuptools"
version = "80.9.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/18/5d/3bf57dcd21979b887f014ea83c24ae194cfcd12b9e0fda66b957c69d1fca/setuptools-80.9.0.tar.gz", hash = "sha256:f36b47402ecde768dbfafc46e8e4207b4360c654f1f3bb84475f0a28628fb19c", size = 1319958, upload-time = "2025-05-27T00:56:51.443Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a3/dc/17031897dae0efacfea57dfd3a82fdd2a2aeb58e0ff71b77b87e44edc772/setuptools-80.9.0-py3-none-any.whl", hash = "sha256:062d34222ad13e0cc312a4c02d73f059e86a4acbfbdea8f8f76b28c99f306922", size = 1201486, upload-time = "2025-05-27T00:56:49.664Z" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/94/e7/b2c673351809dca68a0e064b6af791aa332cf192da575fd474ed7d6f16a2/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81", size = 34031, upload-time = "2024-12-04T17:35:28.174Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274", size = 11050, upload-time = "2024-12-04T17:35:26.475Z" },
]

[[package]]
name = "urllib3"
version = "2.5.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/15/22/9ee70a2574a4f4599c47dd506532914ce044817c7752a79b6a51286319bc/urllib3-2.5.0.tar.gz", hash = "sha256:3fc47733c7e419d4bc3f6b3dc2b4f890bb743906a30d56ba4a5bfa4bbff92760", size = 393185, upload-time = "2025-06-18T14:07:41.644Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl", hash = "sha256:e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc", size = 129795, upload-time = "2025-06-18T14:07:40.39Z" },
]

[[package]]
name = "watchdog"
version = "6.0.0"
source = { registry = "https://pypi.mirrors.ustc.edu.cn/simple/" }
sdist = { url = "https://mirrors.ustc.edu.cn/pypi/packages/db/7d/7f3d619e951c88ed75c6037b246ddcf2d322812ee8ea189be89511721d54/watchdog-6.0.0.tar.gz", hash = "sha256:9ddf7c82fda3ae8e24decda1338ede66e1c99883db93711d8fb941eaa2d8c282", size = 131220, upload-time = "2024-11-01T14:07:13.037Z" }
wheels = [
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/39/ea/3930d07dafc9e286ed356a679aa02d777c06e9bfd1164fa7c19c288a5483/watchdog-6.0.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:bdd4e6f14b8b18c334febb9c4425a878a2ac20efd1e0b231978e7b150f92a948", size = 96471, upload-time = "2024-11-01T14:06:37.745Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/12/87/48361531f70b1f87928b045df868a9fd4e253d9ae087fa4cf3f7113be363/watchdog-6.0.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:c7c15dda13c4eb00d6fb6fc508b3c0ed88b9d5d374056b239c4ad1611125c860", size = 88449, upload-time = "2024-11-01T14:06:39.748Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5b/7e/8f322f5e600812e6f9a31b75d242631068ca8f4ef0582dd3ae6e72daecc8/watchdog-6.0.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:6f10cb2d5902447c7d0da897e2c6768bca89174d0c6e1e30abec5421af97a5b0", size = 89054, upload-time = "2024-11-01T14:06:41.009Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/68/98/b0345cabdce2041a01293ba483333582891a3bd5769b08eceb0d406056ef/watchdog-6.0.0-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:490ab2ef84f11129844c23fb14ecf30ef3d8a6abafd3754a6f75ca1e6654136c", size = 96480, upload-time = "2024-11-01T14:06:42.952Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/85/83/cdf13902c626b28eedef7ec4f10745c52aad8a8fe7eb04ed7b1f111ca20e/watchdog-6.0.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:76aae96b00ae814b181bb25b1b98076d5fc84e8a53cd8885a318b42b6d3a5134", size = 88451, upload-time = "2024-11-01T14:06:45.084Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/fe/c4/225c87bae08c8b9ec99030cd48ae9c4eca050a59bf5c2255853e18c87b50/watchdog-6.0.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:a175f755fc2279e0b7312c0035d52e27211a5bc39719dd529625b1930917345b", size = 89057, upload-time = "2024-11-01T14:06:47.324Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/a9/c7/ca4bf3e518cb57a686b2feb4f55a1892fd9a3dd13f470fca14e00f80ea36/watchdog-6.0.0-py3-none-manylinux2014_aarch64.whl", hash = "sha256:7607498efa04a3542ae3e05e64da8202e58159aa1fa4acddf7678d34a35d4f13", size = 79079, upload-time = "2024-11-01T14:06:59.472Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/5c/51/d46dc9332f9a647593c947b4b88e2381c8dfc0942d15b8edc0310fa4abb1/watchdog-6.0.0-py3-none-manylinux2014_armv7l.whl", hash = "sha256:9041567ee8953024c83343288ccc458fd0a2d811d6a0fd68c4c22609e3490379", size = 79078, upload-time = "2024-11-01T14:07:01.431Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/d4/57/04edbf5e169cd318d5f07b4766fee38e825d64b6913ca157ca32d1a42267/watchdog-6.0.0-py3-none-manylinux2014_i686.whl", hash = "sha256:82dc3e3143c7e38ec49d61af98d6558288c415eac98486a5c581726e0737c00e", size = 79076, upload-time = "2024-11-01T14:07:02.568Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/ab/cc/da8422b300e13cb187d2203f20b9253e91058aaf7db65b74142013478e66/watchdog-6.0.0-py3-none-manylinux2014_ppc64.whl", hash = "sha256:212ac9b8bf1161dc91bd09c048048a95ca3a4c4f5e5d4a7d1b1a7d5752a7f96f", size = 79077, upload-time = "2024-11-01T14:07:03.893Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/2c/3b/b8964e04ae1a025c44ba8e4291f86e97fac443bca31de8bd98d3263d2fcf/watchdog-6.0.0-py3-none-manylinux2014_ppc64le.whl", hash = "sha256:e3df4cbb9a450c6d49318f6d14f4bbc80d763fa587ba46ec86f99f9e6876bb26", size = 79078, upload-time = "2024-11-01T14:07:05.189Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/62/ae/a696eb424bedff7407801c257d4b1afda455fe40821a2be430e173660e81/watchdog-6.0.0-py3-none-manylinux2014_s390x.whl", hash = "sha256:2cce7cfc2008eb51feb6aab51251fd79b85d9894e98ba847408f662b3395ca3c", size = 79077, upload-time = "2024-11-01T14:07:06.376Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/b5/e8/dbf020b4d98251a9860752a094d09a65e1b436ad181faf929983f697048f/watchdog-6.0.0-py3-none-manylinux2014_x86_64.whl", hash = "sha256:20ffe5b202af80ab4266dcd3e91aae72bf2da48c0d33bdb15c66658e685e94e2", size = 79078, upload-time = "2024-11-01T14:07:07.547Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/07/f6/d0e5b343768e8bcb4cda79f0f2f55051bf26177ecd5651f84c07567461cf/watchdog-6.0.0-py3-none-win32.whl", hash = "sha256:07df1fdd701c5d4c8e55ef6cf55b8f0120fe1aef7ef39a1c6fc6bc2e606d517a", size = 79065, upload-time = "2024-11-01T14:07:09.525Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/db/d9/c495884c6e548fce18a8f40568ff120bc3a4b7b99813081c8ac0c936fa64/watchdog-6.0.0-py3-none-win_amd64.whl", hash = "sha256:cbafb470cf848d93b5d013e2ecb245d4aa1c8fd0504e863ccefa32445359d680", size = 79070, upload-time = "2024-11-01T14:07:10.686Z" },
    { url = "https://mirrors.ustc.edu.cn/pypi/packages/33/e8/e40370e6d74ddba47f002a32919d91310d6074130fe4e17dabcafc15cbf1/watchdog-6.0.0-py3-none-win_ia64.whl", hash = "sha256:a1914259fa9e1454315171103c6a30961236f508b9b623eae470268bbcc6a22f", size = 79067, upload-time = "2024-11-01T14:07:11.845Z" },
]

[[package]]
name = "zy"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "keyboard" },
    { name = "pyautogui" },
    { name = "pyinstaller" },
    { name = "pyperclip" },
    { name = "pystray" },
    { name = "requests" },
    { name = "watchdog" },
]

[package.metadata]
requires-dist = [
    { name = "keyboard", specifier = ">=0.13.5" },
    { name = "pyautogui", specifier = ">=0.9.54" },
    { name = "pyinstaller", specifier = ">=6.14.1" },
    { name = "pyperclip", specifier = ">=1.9.0" },
    { name = "pystray", specifier = ">=0.19.5" },
    { name = "requests", specifier = ">=2.32.4" },
    { name = "watchdog" },
]
