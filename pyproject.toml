[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "qrcode-parser"
version = "1.0.0"
description = "一卡通二维码解析系统 - 支持自动扫描和解析"
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.7"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Utilities",
]

dependencies = [
    "requests>=2.25.0",
    "pyperclip>=1.8.0",
    "watchdog>=2.1.0",
    "pyautogui>=0.9.50",
    "keyboard>=0.13.5",
    "pystray>=0.19.0",
    "Pillow>=8.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "black>=21.0.0",
    "flake8>=3.8.0",
    "mypy>=0.800",
]
build = [
    "pyinstaller>=4.0.0",
    "auto-py-to-exe>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/qrcode-parser"
Repository = "https://github.com/yourusername/qrcode-parser"
Issues = "https://github.com/yourusername/qrcode-parser/issues"

[project.scripts]
qrcode-parser = "zy_windows:gui_main"

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]
exclude = ["tests*", "build*", "dist*", "*.egg-info*"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.log"]

[tool.black]
line-length = 88
target-version = ['py37']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
]

[tool.mypy]
python_version = "3.7"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

[[tool.uv.index]]
url = "https://pypi.mirrors.ustc.edu.cn/simple/"
default = true
